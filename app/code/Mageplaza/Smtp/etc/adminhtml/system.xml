<?xml version="1.0"?><!--
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Smtp
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="smtp" translate="label" type="text" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>SMTP</label>
            <tab>mageplaza</tab>
            <resource>Mageplaza_Smtp::configuration</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Configuration</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Mageplaza SMTP</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[Mageplaza SMTP is compatible with default Magento SMTP and other email modules.<br />
                        1. It helps to reduce abandonment cart with <a href="https://www.mageplaza.com/magento-2-one-step-checkout-extension/" target="_blank">One Step Checkout</a>. <br />
                        2. Magento stores see upwards of 30% revenue 💰 with AVADA. <a href="https://go.avada.io/mageplaza">Learn more</a>.]]>
                    </comment>
                </field>
                <field id="log_email" translate="label comment" type="select" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Log Emails</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>It will log all sent emails, you can preview it and schedule clean up.</comment>
                </field>
                <field id="clean_email" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Clean Email Log Every</label>
                    <validate>validate-number validate-zero-or-greater validate-digits</validate>
                    <comment>Day(s). If empty or zero, the Email log will not be cleaned.</comment>
                    <depends>
                        <field id="log_email">1</field>
                    </depends>
                </field>
                <field id="blacklist" translate="label comment" type="textarea" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Blacklist</label>
                    <comment><![CDATA[Enter the main email pattern format. Separated by line breaks.<br />
                        For the email having the same pattern format with this main pattern, it can not receive email from the system.<br />
                        For example: Enter the pattern: /^[0-9][a-z0-9\$\%\&]+@[a-z]+\.[a-z]{2,}$/ means that email format must start with number, the following characters can be normal text, number or special character ($, %, &). The domain after @ with normal-text characters will not receive email from system. For instance: <EMAIL></br>
                        For more details of writing email pattern, please see it <a href=" https://docs.mageplaza.com/smtp-m2/index.html" target="_blank">here</a>.]]>
                    </comment>
                </field>
            </group>
            <group id="configuration_option" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>SMTP Configuration Options</label>
                <field id="host" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Host</label>
                    <comment>Support Host name and IP Address</comment>
                    <frontend_model>Mageplaza\Smtp\Block\Adminhtml\System\Config\Host</frontend_model>
                </field>
                <field id="port" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Port</label>
                    <comment>Default ports: 25, 465, or 587. Port 465 (SSL required), Port 587 (TLS required)</comment>
                </field>
                <field id="protocol" translate="label comment" type="select" sortOrder="35" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Protocol</label>
                    <source_model>Mageplaza\Smtp\Model\Config\Source\Protocol</source_model>
                    <comment>We recommend use security connections: SSL or TLS. Secure Socket Layer (SSL), Transport Layer Security (TLS) protocols.</comment>
                </field>
                <field id="authentication" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Authentication</label>
                    <source_model>Mageplaza\Smtp\Model\Config\Source\Authentication</source_model>
                </field>
                <field id="username" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Username</label>
                </field>
                <field id="password" translate="label" type="obscure" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Password</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                </field>
                <field id="return_path_email" translate="label comment" type="text" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Return-Path Email</label>
                    <comment>Leave empty to ignore it.</comment>
                    <validate>validate-email</validate>
                </field>
                <group id="test_email" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Send Test Email</label>
                    <field id="from" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                        <label>Send From</label>
                        <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                    </field>
                    <field id="to" translate="label" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                        <label>Send To</label>
                        <comment><![CDATA[<p class="note"><span>Enter your email and click <b>Test Now</b> button to test the configuration. <br>
                            Read: <br>
                            - <a href="https://www.mageplaza.com/faqs/smtp/" target="_blank">FAQs</a><br>
                            - <a href="https://www.mageplaza.com/faqs/smtp-connection-timed-out.html" target="_blank">Connection timed out</a>
                        </span>
                        </p>]]>
                        </comment>
                    </field>
                    <field id="sent" translate="label" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                        <label></label>
                        <button_label>Test Now</button_label>
                        <button_url>adminhtml/smtp/test</button_url>
                        <frontend_model>Mageplaza\Smtp\Block\Adminhtml\System\Config\Button</frontend_model>
                    </field>
                </group>
            </group>
            <group id="developer" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Developer</label>
                <field id="developer_mode" translate="label comment" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Developer Mode</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If Enable, Magento will not delivery any email to receiver. This is useful for developers.</comment>
                </field>
            </group>
        </section>
        <tab id="avada" translate="label" sortOrder="401" class="avada-extensions">
            <label>Marketing Automation</label>
        </tab>
        <section id="email_marketing" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Configuration</label>
            <tab>avada</tab>
            <resource>Mageplaza_Smtp::email_marketing</resource>
            <group id="general" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Configuration</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[By enabling <a href="https://avada.io/platform/magento/?utm_source=m2-smtp" target="_blank">Marketing Automation</a>, you agree to the <a href="https://avada.io/tos.html" target="_blank">Terms & Conditions</a> and <a href="https://avada.io/dpa.html" target="_blank">Data processing agreement</a>.]]></comment>
                </field>
                <field id="define_vendor" translate="label comment" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Attribute Code for Vendor</label>
                    <comment><![CDATA[
                    Only applicable with an attribute code </br>
                    Go to <strong>Stores>Attributes>Product</strong> to get field information Attribute Code of <strong>Vendor</strong> attribute
                    ]]></comment>
                    <depends>
                        <field id="email_marketing/general/enabled">1</field>
                    </depends>
                </field>
                <field id="newsletter_subscriber" translate="label comment" type="select" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Sync Newsletter Subscribers</label>
                    <source_model>Mageplaza\Smtp\Model\Config\Source\Newsletter</source_model>
                    <depends>
                        <field id="email_marketing/general/enabled">1</field>
                    </depends>
                </field>
                <field id="app_id" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>App ID</label>
                    <comment><![CDATA[Create API key <a href="https://app.avada.io/integration" target="_blank">here</a>.]]></comment>
                    <depends>
                        <field id="email_marketing/general/enabled">1</field>
                    </depends>
                </field>
                <field id="secret_key" translate="label comment" type="obscure" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Secret key</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="email_marketing/general/enabled">1</field>
                    </depends>
                </field>
                <field id="test_connection" translate="label" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label></label>
                    <button_label>Test connection</button_label>
                    <button_url>adminhtml/smtp/testconnection</button_url>
                    <frontend_model>Mageplaza\Smtp\Block\Adminhtml\System\Config\TestConnection</frontend_model>
                    <depends>
                        <field id="email_marketing/general/enabled">1</field>
                    </depends>
                </field>
                <field id="is_tracking" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Tracking Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="email_marketing/general/enabled">1</field>
                    </depends>
                </field>
                <field id="push_notification" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Push Notification</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="email_marketing/general/enabled">1</field>
                    </depends>
                </field>
                <group id="synchronization" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Synchronization</label>
                    <field id="sync_type" translate="label comment" sortOrder="3" type="select" showInDefault="1" showInStore="1" canRestore="1">
                        <label>Sync type</label>
                        <source_model>Mageplaza\Smtp\Model\Config\Source\SyncType</source_model>
                    </field>
                    <field id="sync_options" translate="label comment" sortOrder="4" type="select" showInDefault="1" showInStore="1" canRestore="1">
                        <label>Choose what to sync</label>
                        <source_model>Mageplaza\Smtp\Model\Config\Source\SyncOptions</source_model>
                        <comment>If "New Object Only" is selected, only sync objects that have never been synced to Marketing Automation App</comment>
                    </field>
                    <field id="days_range" translate="label comment" sortOrder="4" type="select" showInDefault="1" showInStore="1" canRestore="1">
                        <label>Days</label>
                        <source_model>Mageplaza\Smtp\Model\Config\Source\DaysRange</source_model>
                        <comment>If "Choose Date Range" is selected, all the records will be sync if the "Created From - To" is empty.</comment>
                    </field>
                    <field id="created_from" translate="label comment" sortOrder="5" type="text" showInDefault="1" showInStore="1" >
                        <label>Created From</label>
                        <frontend_model>Mageplaza\Smtp\Block\Adminhtml\System\Config\DatePicker</frontend_model>
                        <depends>
                            <field id="email_marketing/general/synchronization/days_range">custom</field>
                        </depends>
                    </field>
                    <field id="sync" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                        <label></label>
                        <button_label>Synchronize Now</button_label>
                        <button_url>adminhtml/smtp_sync/sync</button_url>
                        <frontend_model>Mageplaza\Smtp\Block\Adminhtml\System\Config\Sync</frontend_model>
                    </field>
                    <depends>
                        <field id="email_marketing/general/enabled">1</field>
                    </depends>
                </group>
            </group>
        </section>
    </system>
</config>
