<?php
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Smtp
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */

/** @var \Mageplaza\Smtp\Block\Adminhtml\System\Config\TestConnection $block */
?>
<div class="actions actions-test-email">
    <button class="actions-test-email" type="button" id="<?= $block->getHtmlId() ?>">
        <span><?= $block->escapeHtml($block->getButtonLabel()) ?></span>
    </button>
</div>
<script type="text/x-magento-init">
    {
        "#email_marketing_general_test_connection": {
            "Mageplaza_Smtp/js/testconnection": {
                "ajaxUrl": "<?= $block->escapeHtml($block->getButtonUrl()) ?>"
            }
        }
    }
</script>
