/* Albanian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.sq = {
	closeText: "mbylle",
	prevText: "&#x3C;mbrapa",
	nextText: "Përpara&#x3E;",
	currentText: "sot",
	monthNames: [ "<PERSON><PERSON>", "<PERSON>h<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>",
	"<PERSON>rri<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nëntor", "Dhjetor" ],
	monthNamesShort: [ "<PERSON>", "<PERSON>h<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>hj" ],
	dayNames: [ "<PERSON>", "<PERSON>ënë", "<PERSON> Martë", "<PERSON> Mërkurë", "E Enjte", "E Premte", "E Shtune" ],
	dayNamesShort: [ "Di", "Hë", "Ma", "Më", "En", "Pr", "Sh" ],
	dayNamesMin: [ "Di", "Hë", "Ma", "Më", "En", "Pr", "Sh" ],
	weekHeader: "Ja",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.sq );

return datepicker.regional.sq;

} );
