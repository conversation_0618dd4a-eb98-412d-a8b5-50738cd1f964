!function(o){o.isScrollToFixed=function(i){return!!o(i).data("ScrollToFixed")},o.ScrollToFixed=function(i,e){var t=this;t.$el=o(i),t.el=i,t.$el.data("ScrollToFixed",t);var n,s,l,r,d=!1,p=t.$el,c=0,x=0,f=-1,a=-1,u=null;function F(){var o=t.options.limit;return o?"function"==typeof o?o.apply(p):o:0}function g(){return"fixed"===n}function T(){return"absolute"===n}function S(){return!(g()||T())}function b(){if(!g()){var o=p[0].getBoundingClientRect();u.css({display:p.css("display"),width:o.width,height:o.height,float:p.css("float")}),cssOptions={"z-index":t.options.zIndex,position:"fixed",top:-1==t.options.bottom?v():"",bottom:-1==t.options.bottom?"":t.options.bottom,"margin-left":"0px"},t.options.dontSetWidth||(cssOptions.width=p.css("width")),p.css(cssOptions),p.addClass(t.options.baseClassName),t.options.className&&p.addClass(t.options.className),n="fixed"}}function m(){var o=F(),i=x;t.options.removeOffsets&&(i="",o-=c),cssOptions={position:"absolute",top:o,left:i,"margin-left":"0px",bottom:""},t.options.dontSetWidth||(cssOptions.width=p.css("width")),p.css(cssOptions),n="absolute"}function w(){S()||(a=-1,u.css("display","none"),p.css({"z-index":r,width:"",position:s,left:"",top:l,"margin-left":""}),p.removeClass("scroll-to-fixed-fixed"),t.options.className&&p.removeClass(t.options.className),n=null)}function h(o){o!=a&&(p.css("left",x-o),a=o)}function v(){var o=t.options.marginTop;return o?"function"==typeof o?o.apply(p):o:0}function U(){if(o.isScrollToFixed(p)&&!p.is(":hidden")){var i=d,e=S();d?S()&&(c=p.offset().top,x=p.offset().left):(p.trigger("preUnfixed.ScrollToFixed"),w(),p.trigger("unfixed.ScrollToFixed"),a=-1,c=p.offset().top,x=p.offset().left,t.options.offsets&&(x+=p.offset().left-p.position().left),-1==f&&(f=x),n=p.css("position"),d=!0,-1!=t.options.bottom&&(p.trigger("preFixed.ScrollToFixed"),b(),p.trigger("fixed.ScrollToFixed")));var l=o(window).scrollLeft(),r=o(window).scrollTop(),u=F();t.options.minWidth&&o(window).width()<t.options.minWidth?S()&&i||(z(),p.trigger("preUnfixed.ScrollToFixed"),w(),p.trigger("unfixed.ScrollToFixed")):t.options.maxWidth&&o(window).width()>t.options.maxWidth?S()&&i||(z(),p.trigger("preUnfixed.ScrollToFixed"),w(),p.trigger("unfixed.ScrollToFixed")):-1==t.options.bottom?u>0&&r>=u-v()?e||T()&&i||(z(),p.trigger("preAbsolute.ScrollToFixed"),m(),p.trigger("unfixed.ScrollToFixed")):r>=c-v()&&(!t.options.minTop||o(window).scrollTop()>t.options.minTop)?(g()&&i||(z(),p.trigger("preFixed.ScrollToFixed"),b(),a=-1,p.trigger("fixed.ScrollToFixed")),h(l)):S()&&i||(z(),p.trigger("preUnfixed.ScrollToFixed"),w(),p.trigger("unfixed.ScrollToFixed")):u>0?r+o(window).height()-p.outerHeight(!0)>=u-(v()||-(t.options.bottom?t.options.bottom:0))?g()&&(z(),p.trigger("preUnfixed.ScrollToFixed"),"absolute"===s?m():w(),p.trigger("unfixed.ScrollToFixed")):(g()||(z(),p.trigger("preFixed.ScrollToFixed"),b()),h(l),p.trigger("fixed.ScrollToFixed")):h(l)}}function z(){var o=p.css("position");"absolute"==o?p.trigger("postAbsolute.ScrollToFixed"):"fixed"==o?p.trigger("postFixed.ScrollToFixed"):p.trigger("postUnfixed.ScrollToFixed")}var A=function(o){p.is(":visible")?(d=!1,U()):w()},C=function(o){window.requestAnimationFrame?requestAnimationFrame(U):U()};t.init=function(){t.options=o.extend({},o.ScrollToFixed.defaultOptions,e),r=p.css("z-index"),t.$el.css("z-index",t.options.zIndex),u=o("<div />"),n=p.css("position"),s=p.css("position"),p.css("float"),l=p.css("top"),S()&&t.$el.after(u),o(window).bind("resize.ScrollToFixed",A),o(window).bind("scroll.ScrollToFixed",C),"ontouchmove"in window&&o(window).bind("touchmove.ScrollToFixed",U),t.options.preFixed&&p.bind("preFixed.ScrollToFixed",t.options.preFixed),t.options.postFixed&&p.bind("postFixed.ScrollToFixed",t.options.postFixed),t.options.preUnfixed&&p.bind("preUnfixed.ScrollToFixed",t.options.preUnfixed),t.options.postUnfixed&&p.bind("postUnfixed.ScrollToFixed",t.options.postUnfixed),t.options.preAbsolute&&p.bind("preAbsolute.ScrollToFixed",t.options.preAbsolute),t.options.postAbsolute&&p.bind("postAbsolute.ScrollToFixed",t.options.postAbsolute),t.options.fixed&&p.bind("fixed.ScrollToFixed",t.options.fixed),t.options.unfixed&&p.bind("unfixed.ScrollToFixed",t.options.unfixed),t.options.spacerClass&&u.addClass(t.options.spacerClass),p.bind("resize.ScrollToFixed",function(){u.height(p.height())}),p.bind("scroll.ScrollToFixed",function(){p.trigger("preUnfixed.ScrollToFixed"),w(),p.trigger("unfixed.ScrollToFixed"),U()}),p.bind("detach.ScrollToFixed",function(i){var e;(e=(e=i)||window.event).preventDefault&&e.preventDefault(),e.returnValue=!1,p.trigger("preUnfixed.ScrollToFixed"),w(),p.trigger("unfixed.ScrollToFixed"),o(window).unbind("resize.ScrollToFixed",A),o(window).unbind("scroll.ScrollToFixed",C),p.unbind(".ScrollToFixed"),u.remove(),t.$el.removeData("ScrollToFixed")}),A()},t.init()},o.ScrollToFixed.defaultOptions={minTop:0,marginTop:0,limit:0,bottom:-1,zIndex:1e3,baseClassName:"scroll-to-fixed-fixed"},o.fn.scrollToFixed=function(i){return this.each(function(){new o.ScrollToFixed(this,i)})}}(jQuery);