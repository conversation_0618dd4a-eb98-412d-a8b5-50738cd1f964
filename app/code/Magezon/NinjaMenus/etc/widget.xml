<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_NinjaMenus
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
    <widget id="ninjamenus_menu" class="Magezon\NinjaMenus\Block\Widget\Menu">
        <label translate="true">NinjaMenus Menu</label>
        <description translate="true"></description>
        <parameters>
            <parameter name="title" xsi:type="text" visible="true">
                <label translate="true">Title</label>
            </parameter>
            <parameter name="identifier" xsi:type="block" visible="true" required="true">
                <label translate="true">Menu</label>
                <block class="Magezon\NinjaMenus\Block\Adminhtml\Menu\Widget\Chooser">
                    <data>
                        <item name="button" xsi:type="array">
                            <item name="open" xsi:type="string" translate="true">Select Menu...</item>
                        </item>
                    </data>
                </block>
            </parameter>
        </parameters>
    </widget>
</widgets>