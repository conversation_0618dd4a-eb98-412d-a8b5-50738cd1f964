/**
 * A simple jQuery plugin for creating animated drilldown menus.
 *
 * @name jQuery Drilldown
 * @version 1.1.1
 * @requires jQuery v1.7+
 * <AUTHOR>
 * @license [MIT]{@link http://opensource.org/licenses/mit-license.php}
 *
 * Copyright (c) 2015 Aleksandras Nelkinas
 */
!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?module.exports=t(require("jquery")):t(jQuery)}(function(t,s){"use strict";var i="drilldown",e="data-next-parent",n={event:"click",selector:"a",speed:100,cssClass:{container:i+"-container",root:i+"-root",sub:i+"-sub",back:i+"-back"}},o=function(){function o(s,e){var o=this;this._name=i,this._defaults=n,this.element=s,this.$element=t(s),this.options=t.extend({},n,e),this._history=[],this._css={"float":"left",width:null},this.$container=this.$element.find("."+this.options.cssClass.container),this.$element.on(this.options.event+"."+i,this.options.selector,function(s){h.call(o,s,t(this))})}function h(t,s){var i=s.nextAll("."+this.options.cssClass.sub),e=!0;i.length?a.call(this,i):s.closest("."+this.options.cssClass.back).length?r.call(this):e=!1,e&&"A"===s.prop("tagName")&&t.preventDefault()}function a(t,i){var n=i&&i.speed!==s?i.speed:this.options.speed;t.length&&(this._css.width=this.$element.outerWidth(),this.$container.width(2*this._css.width),t.parent().attr(e,!0),t=t.removeClass(this.options.cssClass.sub).addClass(this.options.cssClass.root),this.$container.append(t),c.call(this,{marginLeft:-1*this._css.width,speed:n},function(){var s=t.prev();this._history.push(s.detach()),l.call(this,t)}.bind(this)))}function r(t){var i=t&&t.speed!==s?t.speed:this.options.speed,n=this._history.pop();this._css.width=this.$element.outerWidth(),this.$container.width(2*this._css.width),this.$container.prepend(n),c.call(this,{marginLeft:0,speed:i},function(){var t=n.next();t.addClass(this.options.cssClass.sub).removeClass(this.options.cssClass.root),this.$container.find("["+e+"]").last().removeAttr(e).append(t),l.call(this,n)}.bind(this))}function c(t,s){var i=this.$container.children("."+this.options.cssClass.root);i.css(this._css),i.first().animate({marginLeft:t.marginLeft},t.speed,s)}function l(t){t.css({"float":"",width:"",marginLeft:""}),this.$container.css("width","")}return o.prototype={destroy:function(){this.reset(),this.$element.off(this.options.event+"."+i,this.options.selector)},reset:function(){var t;for(t=this._history.length;t>0;t--)r.call(this,{speed:0});this._history=[],this._css={"float":"left",width:null}}},o}();t.fn[i]=function(s){return this.each(function(){var e=t.data(this,i),n=s;e?"string"==typeof n&&("destroy"===n&&t.removeData(this,i),"function"==typeof e[n]&&e[n]()):t.data(this,i,new o(this,s))})}});