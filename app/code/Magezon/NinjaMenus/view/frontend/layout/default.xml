<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_NinjaMenus
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<head>
		<css src="Magezon_NinjaMenus::css/styles.css"/>
    </head>
    <body>
        <referenceBlock name="store.menu">
            <block class="Magezon\NinjaMenus\Block\TopMenu" name="catalog.topnav" template="topmenu.phtml" before="-">
                <arguments>
                    <argument name="identifier" translate="true" xsi:type="string">top-menu</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>