<?php

namespace MadHat\OldUrlRewrite\Model;

use Magento\Catalog\Model\ProductRepository;
use Magento\Framework\App\Request\Http;
use Magento\Framework\Exception\NoSuchEntityException;

class RedirectManager
{
    protected $urlRewriteFactory;
    protected $productRepository;
    protected $request;

    public function __construct(
        UrlRewriteFactory $urlRewriteFactory,
        ProductRepository $productRepository,
        Http              $request
    ) {
        $this->urlRewriteFactory = $urlRewriteFactory;
        $this->productRepository = $productRepository;
        $this->request = $request;
    }

    public function redirectOldUrlToNew(): ?string
    {
        $oldUrl = $this->request->getRequestUri();

        if (!str_contains($oldUrl, "?variant=")) {
            return null;
        }

        $urlRewrite = $this->urlRewriteFactory->create();
        $urlRewrite->load($oldUrl, 'old_url');

        if ($urlRewrite->getId()) {
            $sku = $urlRewrite->getSku();
            $newProductUrl = $this->getNewProductUrlBySku($sku);
            if ($newProductUrl) {
                return $newProductUrl; // Return the new URL instead of redirecting
            }
        }

        return null; // No redirection needed
    }

    protected function getNewProductUrlBySku($sku): ?string
    {
        try {
            $product = $this->productRepository->get($sku);
            return $product->getProductUrl();
        } catch (NoSuchEntityException $e) {
            return null;
        }
    }
}
