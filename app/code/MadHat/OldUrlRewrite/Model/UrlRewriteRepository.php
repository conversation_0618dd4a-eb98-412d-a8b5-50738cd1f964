<?php
declare(strict_types=1);

namespace MadHat\OldUrlRewrite\Model;

use MadHat\OldUrlRewrite\Api\Data\UrlRewriteInterface;
use MadHat\OldUrlRewrite\Api\Data\UrlRewriteInterfaceFactory;
use MadHat\OldUrlRewrite\Api\Data\UrlRewriteSearchResultsInterface;
use MadHat\OldUrlRewrite\Api\Data\UrlRewriteSearchResultsInterfaceFactory;
use MadHat\OldUrlRewrite\Api\UrlRewriteRepositoryInterface;
use MadHat\OldUrlRewrite\Model\ResourceModel\UrlRewrite as UrlRewriteResource;
use MadHat\OldUrlRewrite\Model\ResourceModel\UrlRewrite\CollectionFactory as UrlRewriteCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class UrlRewriteRepository implements UrlRewriteRepositoryInterface
{

    /**
     * @var UrlRewriteResource
     */
    protected UrlRewriteResource $resource;

    /**
     * @var UrlRewriteSearchResultsInterfaceFactory
     */
    protected UrlRewriteSearchResultsInterfaceFactory $searchResultsFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected CollectionProcessorInterface $collectionProcessor;

    /**
     * @var UrlRewriteInterfaceFactory
     */
    protected UrlRewriteInterfaceFactory $urlRewriteFactory;

    /**
     * @var UrlRewriteCollectionFactory
     */
    protected UrlRewriteCollectionFactory $urlRewriteCollectionFactory;

    /**
     * @param UrlRewriteResource $resource
     * @param UrlRewriteInterfaceFactory $urlRewriteFactory
     * @param UrlRewriteCollectionFactory $urlRewriteCollectionFactory
     * @param UrlRewriteSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        UrlRewriteResource $resource,
        UrlRewriteInterfaceFactory $urlRewriteFactory,
        UrlRewriteCollectionFactory $urlRewriteCollectionFactory,
        UrlRewriteSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->urlRewriteFactory = $urlRewriteFactory;
        $this->urlRewriteCollectionFactory = $urlRewriteCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(UrlRewriteInterface $urlRewrite): UrlRewriteInterface
    {
        try {
            $this->resource->save($urlRewrite);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the urlRewrite: %1',
                $exception->getMessage()
            ));
        }
        return $urlRewrite;
    }

    /**
     * @inheritDoc
     */
    public function get(mixed $urlRewriteId): UrlRewriteInterface
    {
        $urlRewrite = $this->urlRewriteFactory->create();
        $this->resource->load($urlRewrite, $urlRewriteId);
        if (!$urlRewrite->getId()) {
            throw new NoSuchEntityException(__('UrlRewrite with id "%1" does not exist.', $urlrewriteId));
        }
        return $urlRewrite;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        SearchCriteriaInterface $criteria
    ): UrlRewriteSearchResultsInterface {
        $collection = $this->urlRewriteCollectionFactory->create();

        $this->collectionProcessor->process($criteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);

        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }

        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(UrlRewriteInterface $urlRewrite): bool
    {
        try {
            $urlRewriteModel = $this->urlRewriteFactory->create();
            $this->resource->load($urlRewriteModel, $urlRewrite->getEntityId());
            $this->resource->delete($urlRewriteModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the UrlRewrite: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById(mixed $urlRewriteId): bool
    {
        return $this->delete($this->get($urlRewriteId));
    }
}
