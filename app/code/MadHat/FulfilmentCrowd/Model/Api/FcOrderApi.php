<?php
declare(strict_types=1);

namespace MadHat\FulfilmentCrowd\Model\Api;

use Exception;
use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use MadHat\FulfilmentCrowd\Helper\Data;
use MadHat\FulfilmentCrowd\Logger\Logger;
use MadHat\OrderIntegration\Api\MadhatOrderInfoRepositoryInterface;
use MadHat\OrderIntegration\Model\OrderGridRefresher;
use MadHat\SiteIntegrationOrder\Helper\Data as SiteOrderHelper;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\State\InputMismatchException;
use Magento\Framework\Exception\StateException;
use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as MagentoOrderCollectionFactory;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\StoreManagerInterface;

class FcOrderApi extends FulfilmentCrowdApi
{
    /**
     * @var array
     */
    protected array $shippingMethodsMapping = [];

    /**
     * @var DbLoggerSaver
     */
    protected DbLoggerSaver $dbLoggerSaver;

    /**
     * @var FcCustomerApi
     */
    protected FcCustomerApi $fcCustomerApi;

    /**
     * @var FcProductApi
     */
    protected FcProductApi $fcProductApi;

    /**
     * @var MadhatOrderInfoRepositoryInterface
     */
    protected MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository;

    /**
     * @var OrderGridRefresher
     */
    protected OrderGridRefresher $orderGridRefresher;

    /**
     * @var SiteOrderHelper
     */
    protected SiteOrderHelper $siteOrderHelper;

    /**
     * @var ProductRepositoryInterface
     */
    protected ProductRepositoryInterface $productRepository;

    /**
     * @var CustomerRepositoryInterface
     */
    protected CustomerRepositoryInterface $customerRepository;

    /**
     * @var MagentoOrderCollectionFactory
     */
    protected MagentoOrderCollectionFactory $magentoOrderCollectionFactory;

    /**
     * @var Emulation
     */
    protected Emulation $emulation;

    /**
     * @var ScopeConfigInterface
     */
    protected ScopeConfigInterface $scopeConfig;

    /**
     * @param Data $fcHelper
     * @param Logger $fcLogger
     * @param Curl $curl
     * @param Json $json
     * @param StoreManagerInterface $storeManager
     * @param DbLoggerSaver $dbLoggerSaver
     * @param FcCustomerApi $fcCustomerApi
     * @param FcProductApi $fcProductApi
     * @param MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository
     * @param OrderGridRefresher $orderGridRefresher
     * @param SiteOrderHelper $siteOrderHelper
     * @param ProductRepositoryInterface $productRepository
     * @param CustomerRepositoryInterface $customerRepository
     * @param MagentoOrderCollectionFactory $magentoOrderCollectionFactory
     * @param Emulation $emulation
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        Data $fcHelper,
        Logger $fcLogger,
        Curl $curl,
        Json $json,
        StoreManagerInterface $storeManager,
        DbLoggerSaver $dbLoggerSaver,
        FcCustomerApi $fcCustomerApi,
        FcProductApi $fcProductApi,
        MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository,
        OrderGridRefresher $orderGridRefresher,
        SiteOrderHelper $siteOrderHelper,
        ProductRepositoryInterface $productRepository,
        CustomerRepositoryInterface $customerRepository,
        MagentoOrderCollectionFactory $magentoOrderCollectionFactory,
        Emulation $emulation,
        ScopeConfigInterface $scopeConfig
    ) {
        parent::__construct(
            $fcHelper,
            $fcLogger,
            $curl,
            $json,
            $storeManager
        );
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->fcCustomerApi = $fcCustomerApi;
        $this->fcProductApi = $fcProductApi;
        $this->madhatOrderInfoRepository = $madhatOrderInfoRepository;
        $this->orderGridRefresher = $orderGridRefresher;
        $this->siteOrderHelper = $siteOrderHelper;
        $this->productRepository = $productRepository;
        $this->customerRepository = $customerRepository;
        $this->magentoOrderCollectionFactory = $magentoOrderCollectionFactory;
        $this->emulation = $emulation;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Export Order to Fulfilment Crowd
     *
     * @param array $orderIds
     * @return array
     */
    public function exportFcOrders(array $orderIds = []): array
    {
        $filteredOrderIds = $this->filterOrderCollection($orderIds);

        $result = [
            'success' => false,
            'total_orders' => count($filteredOrderIds),
            'success_orders' => 0,
            'failed_orders' => 0,
            'error_messages' => []
        ];

        if (empty($filteredOrderIds)) {
            $this->fcLogger->info(__('No orders found to export.'));
            return $result;
        }

        $shippingMethodsMapping = $this->getShippingMethodsMapping();

        if (empty($shippingMethodsMapping)) {
            $result['failed_orders'] = count($filteredOrderIds);
            $result['error_messages'][] = __('Orders can\'t exported because of missing shipping methods mapping.');

            $this->logOrderExportReportToDbLogger($result);

            $this->fcLogger->critical(__('Orders can\'t exported because of missing shipping methods mapping.'));

            $result['error_messages'] = [
                [
                    'WebOrderId' => 'Critical Error',
                    'ErrorMessage' => __('Orders can\'t exported because of missing shipping methods mapping.')
                ]
            ];

            return $result;
        }

        $magentoOrderCollection = $this->magentoOrderCollectionFactory->create()
            ->addFieldToFilter('entity_id', ['in' => $filteredOrderIds]);

        $this->fcLogger->info(__("FC Order Export Process Starts"));
        $this->fcLogger->info(__('FC Orders export started for {%1}.', implode(', ', $filteredOrderIds)));

        foreach ($magentoOrderCollection as $order) {
            try {
                $fcOrderParams = $this->createFulfilmentCrowdParams($order);
                $fcOrderResponse = $this->createFcOrderApi($fcOrderParams);
                if (isset($fcOrderResponse['id'])) {
                    $this->saveFcOrderData($order, $fcOrderResponse);
                    $result['success_orders']++;
                } elseif (isset($fcOrderResponse['error'])) {
                    $result['error_messages'][] = [
                        'WebOrderId' => $order->getEntityId(),
                        'ErrorMessage' => $fcOrderResponse['error']['message'],
                    ];
                    $result['failed_orders']++;
                } else {
                    $result['error_messages'][] = [
                        'WebOrderId' => $order->getEntityId(),
                        'ErrorMessage' => __(
                            'Unable to export order to Fulfillment Crowd for Order ID : %1',
                            $order->getId()
                        )
                    ];
                    $this->fcLogger->info(
                        __(
                            "Order %1 not exported to Fulfillment Crowd. Error Response : %2",
                            $order->getId(),
                            print_r($fcOrderResponse, true)
                        )
                    );
                    $result['failed_orders']++;
                }
            } catch (Exception $e) {
                $this->fcLogger->info(
                    __(
                        "(Magento Order ID : %1) Exception Occurred during FC Order Export: %2 ",
                        $order->getEntityId(),
                        $e->getMessage()
                    )
                );
                $result['error_messages'][] = [
                    'WebOrderId' => $order->getEntityId(),
                    'ErrorMessage' => $e->getMessage()
                ];
                $result['failed_orders']++;
            }
        }

        if ($result['success_orders'] > 0) {
            $result['success'] = true;
        }

        $this->logOrderExportReportToDbLogger($result);

        if ($result['success']) {
            $result['message'] = __(
                '%1 out of %2 order\'s exported to Fulfilment Crowd.',
                $result['success_orders'],
                $result['total_orders']
            );
        }

        $this->fcLogger->info(__("FC Order Export Process Ends"));

        return $result;
    }

    /**
     * Filter Order Collection
     *  - Check SITE_Order_ID is not NULL
     *  - Check SITE_Order status is Picked
     *  - Check FC_Order_ID is NULL
     *  - Check amount (SITE_IS_CAPTURED) is captured and updated on SITE (in case of Adyen Payment Methods)
     *  - Ignore SITE_IS_CAPTURED for offline payments.
     *  - Skip orders with FulfilmentWorkflow = "Ongoing"
     *
     * @param array $orderIds
     * @return array
     */
    protected function filterOrderCollection(array $orderIds = []): array
    {
        $orderCollection = $this->magentoOrderCollectionFactory->create();
        $allowedOfflinePaymentMethods = $this->siteOrderHelper->getAllowedOfflinePaymentMethodsFromConfig('1');

        $orderCollection->addFieldToSelect(['increment_id','status'])
            ->getSelect()
            ->join(
                ['sop' => $orderCollection->getTable('sales_order_payment')], // Join sales_order_payment table
                'main_table.entity_id = sop.parent_id',
                ['cc_trans_id','method']
            )
            ->joinLeft(
                ['si' => $orderCollection->getTable('sales_invoice')], // Join sales_order_payment table
                'main_table.entity_id = si.order_id',
                ['order_invoice_id' => 'si.entity_id']
            )
            ->joinLeft(
                ['an' => $orderCollection->getTable('adyen_notification')], // Join adyen_notification table
                'an.original_reference = sop.cc_trans_id AND an.merchant_reference = main_table.increment_id',
                ['event_code', 'success', 'done']
            )
            ->join(
                ['moi' => $orderCollection->getTable('madhat_order_info')], // Join madhat_order_info table
                'moi.order_id = main_table.entity_id',
                ['site_order_id', 'site_is_picked', 'site_is_captured','fc_order_id', 'fulfilment_workflow']
            );

        // Add status filter
        $orderCollection->addFieldToFilter('status', ['nin' => ['canceled']])
            ->addFieldToFilter('moi.site_order_id', ['notnull' => true])
            ->addFieldToFilter('moi.site_is_picked', true)
            ->addFieldToFilter('moi.fc_order_id', ['null' => true]);

        // Add conditional logic for strict Adyen checks and allowed offline payment methods
        $connection = $orderCollection->getConnection();
        $select = $orderCollection->getSelect();
        $select->where('moi.fulfilment_workflow = "FulfilmentCrowd"'
        );

        // Add conditional logic for strict Adyen checks and allowed offline payment methods
        $select->where(
            '(' .
            $connection->quoteInto('sop.method IN (?)', $allowedOfflinePaymentMethods) .
            ' OR (' .
            'moi.site_is_captured is TRUE AND sop.cc_trans_id IS NOT NULL AND ' .
            $connection->quoteInto('an.event_code = ?', 'CAPTURE') . ' AND ' .
            $connection->quoteInto('an.success = ?', 'true') . ' AND ' .
            $connection->quoteInto('an.done = ?', '1') .
            ')' .
            ')'
        );

        if (!empty($orderIds)) {
            $orderCollection->addFieldToFilter('entity_id', ['in' => $orderIds]);
        }

        $orderIds = $orderCollection->getColumnValues('entity_id');

        if (empty($orderIds)) {
            return [];
        }

        return $orderIds;
    }

    /**
     * Return shipping method mapping for standard/express delivery method.
     *
     * @return array
     */
    private function getShippingMethodsMapping(): array
    {
        if (empty($this->shippingMethodsMapping)) {
            $this->shippingMethodsMapping = $this->fcHelper->getShippingMethodsMapping();
        }
        return $this->shippingMethodsMapping;
    }

    /**
     * Create params for Fulfilment Crowd Order Export
     *
     * @param Order $order
     * @return array
     * @throws LocalizedException
     */
    public function createFulfilmentCrowdParams(Order $order): array
    {
        $fulfilmentCrowdOrderParams = [];
        $fulfilmentCrowdOrderParams = $this->addCustomerData($fulfilmentCrowdOrderParams, $order);
        $fulfilmentCrowdOrderParams = $this->addDeliveryAddressData($fulfilmentCrowdOrderParams, $order);
        $fulfilmentCrowdOrderParams = $this->addOrderLinesData($fulfilmentCrowdOrderParams, $order);

        return $fulfilmentCrowdOrderParams;
    }

    /**
     * Create customer data params for Fulfilment Crowd Order Export
     *
     * @param array $params
     * @param OrderInterface $order
     * @return array
     * @throws InputException
     * @throws InputMismatchException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function addCustomerData(array $params, OrderInterface $order): array
    {
        $fcCustomerId = $this->getFcCustomerIdForOrder($order);
        $params = [
            'customer_id' => $fcCustomerId,
            'customer_address_contact_name' => $order->getCustomerName(),
            'customer_address_line1' => $order->getBillingAddress()->getStreet()[0],
            'customer_address_line2' => $order->getBillingAddress()?->getStreet()[1] ?? '',
            'customer_address_line3' => $order->getBillingAddress()->getCity(),
            'customer_address_line4' => $order->getBillingAddress()->getRegion(),
            'customer_address_postcode' => $order->getBillingAddress()->getPostcode(),
            'customer_address_contact_email' => $order->getBillingAddress()->getEmail(),
            'customer_address_contact_phone' => $order->getBillingAddress()->getTelephone(),
        ];
        return $params;
    }

    /**
     * Create delivery data params for Fulfilment Crowd Order Export
     *
     * @param array $params
     * @param OrderInterface $order
     * @return array
     */
    private function addDeliveryAddressData(array $params, OrderInterface $order): array
    {
        $params['delivery_address_contact_name'] = $order->getCustomerName();
        $params['delivery_address_country_code'] = $order->getShippingAddress()->getCountryId();
        $params['delivery_address_line1'] = $order->getShippingAddress()->getStreet()[0];
        $params['delivery_address_line2'] = $order->getShippingAddress()->getStreet()[1] ?? '';
        $params['delivery_address_line3'] = $order->getShippingAddress()->getCity();
        $params['delivery_address_line4'] = $order->getShippingAddress()->getRegion();
        $params['delivery_address_postcode'] = $order->getShippingAddress()->getPostcode();
        $params['delivery_address_contact_email'] = $order->getShippingAddress()->getEmail();
        $params['delivery_address_contact_phone'] = $order->getShippingAddress()->getTelephone();
        $params['delivery_address_contact_mobile'] = $order->getShippingAddress()->getTelephone();
        $params['delivery_address_organisation_name'] = $order->getShippingAddress()->getCompany();
        return $params;
    }

    /**
     * Create order lines params for Fulfilment Crowd Order Export
     *
     * @param array $params
     * @param OrderInterface $order
     * @return array
     * @throws LocalizedException
     */
    private function addOrderLinesData(array $params, OrderInterface $order): array
    {
        $orderLines = [];
        foreach ($order->getItems() as $orderItem) {
            if ($orderItem->getProductType() == 'configurable') {
                continue;
            }
            $price = $orderItem->getPrice();
            if ($orderItem->getParentItem()) {
                $price = $orderItem->getParentItem()->getPrice();
            }
            $orderLines[] = [
                'quantity' => (int)$orderItem->getQtyOrdered(),
                'line_description' => $orderItem->getProductName(),
                'parent_order_line_id' => 0,
                'product_id' => $this->getFcProductId($orderItem->getProduct()),
                'product_code' => $orderItem->getSku(),
                'product_cost' => $orderItem->getPrice(),
                'product_cost_iso4217_currency_code' => $orderItem->getCurrencyCode(),
                'product_description' => $orderItem->getProductName(),
                'price' => $price,
            ];
        }

        $params['imported_reference'] = $order->getEntityId();
        $params['alternative_reference'] = $order->getIncrementId();
        $params['channel_id'] = $this->fcHelper->getChannelId();
        $params['dispatch_service_charge'] = 0;

        $shippingMethod = $order->getShippingDescription();

        if (!isset($this->shippingMethodsMapping[$shippingMethod])) {
            throw new LocalizedException(
                __("Shipping Method missing for %1", $shippingMethod)
            );
        }

        $fcDeliveryMethod = $this->shippingMethodsMapping[$shippingMethod];
        $params[$fcDeliveryMethod] = true;

        $params["order_lines"] = $orderLines;
        return $params;
    }

    /**
     * Get fc_customer_id for order customer
     *
     * @param OrderInterface $order
     * @return mixed
     * @throws InputException
     * @throws InputMismatchException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function getFcCustomerIdForOrder(OrderInterface $order): mixed
    {
        $customerId = $order->getCustomerId();
        if ($customerId) {
            $fcCustomer = $this->customerRepository->getById($customerId)
                ->getCustomAttribute('fc_customer_id');

            if ($fcCustomer) {
                return $fcCustomer->getValue();
            } else {
                return $this->fcCustomerApi->getFcCustomerByOrder($order);
            }
        } else {
            return $this->fcCustomerApi->getFcCustomerByOrder($order);
        }
    }

    /**
     * Get fc_product_id for product
     *
     * @param Product|null $product
     * @return mixed
     * @throws CouldNotSaveException
     * @throws InputException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @throws StateException
     */
    protected function getFcProductId(?Product $product): mixed
    {
        $productSku = $product->getSku();
        $fcProduct = $this->productRepository->get($productSku)->getCustomAttribute('fc_product_id');

        if ($fcProduct) {
            return $fcProduct->getValue();
        } else {
            return $this->fcProductApi->fetchFcProductId($product);
        }
    }

    /**
     * Save fc_order_id in madhat_order_info for Order
     *
     * @param OrderInterface $order
     * @param mixed $fcOrderData
     * @return void
     * @throws CouldNotSaveException
     * @throws NoSuchEntityException
     */
    protected function saveFcOrderData(OrderInterface $order, mixed $fcOrderData): void
    {
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId((int) $order->getId());
        $madhatOrderInfo->setFcOrderId((int) $fcOrderData['id']);
        $madhatOrderInfo->setFcOrderRefId((int) $fcOrderData['reference']);
        $this->madhatOrderInfoRepository->save($madhatOrderInfo);

        $this->orderGridRefresher->refresh($order->getId());
    }

    /**
     * Log FC order export report in DBLogger
     *
     * @param array $dbLoggerData
     * @return void
     */
    protected function logOrderExportReportToDbLogger(array $dbLoggerData): void
    {
        $message = "Total Order's : " . $dbLoggerData['total_orders'];
        $message .= " | Success Order's : " . $dbLoggerData['success_orders'];
        $message .= " | Failed Order's : " . $dbLoggerData['failed_orders'];

        if (!empty($dbLoggerData)) {
            $message .= " | Errors : " . $this->json->serialize($dbLoggerData['error_messages']);
        }

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation($defaultStoreId);
        $this->dbLoggerSaver->addRecord(
            'Fulfilment Crowd Order Export Report',
            $message,
            'NOTICE',
            LogIdentifierProvider::ORDER
        );
        $this->emulation->stopEnvironmentEmulation();
    }
}
