<?php
declare(strict_types=1);

namespace MadHat\FulfilmentCrowd\Model\Api;

use Exception;
use MadHat\FulfilmentCrowd\Helper\Data;
use MadHat\FulfilmentCrowd\Logger\Logger;
use MadHat\FulfilmentCrowd\Model\ResourceModel\FcCountry\CollectionFactory;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerSearchResultsInterface;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\State\InputMismatchException;
use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Store\Model\StoreManagerInterface;

class FcCustomer<PERSON><PERSON> extends FulfilmentCrowdApi
{
    /**
     * @var CollectionFactory
     */
    protected CollectionFactory $fcCountryCollectionFactory;

    /**
     * @var AddressRepositoryInterface
     */
    protected AddressRepositoryInterface $addressRepository;

    /**
     * @var CustomerRepositoryInterface
     */
    protected CustomerRepositoryInterface $customerRepository;

    /**
     * @param Data $fcHelper
     * @param Logger $fcLogger
     * @param Curl $curl
     * @param Json $json
     * @param StoreManagerInterface $storeManager
     * @param CollectionFactory $fcCountryCollectionFactory
     * @param AddressRepositoryInterface $addressRepository
     * @param CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        Data                        $fcHelper,
        Logger                      $fcLogger,
        Curl                        $curl,
        Json                        $json,
        StoreManagerInterface       $storeManager,
        CollectionFactory           $fcCountryCollectionFactory,
        AddressRepositoryInterface  $addressRepository,
        CustomerRepositoryInterface $customerRepository
    ) {
        parent::__construct(
            $fcHelper,
            $fcLogger,
            $curl,
            $json,
            $storeManager
        );
        $this->fcCountryCollectionFactory = $fcCountryCollectionFactory;
        $this->addressRepository = $addressRepository;
        $this->customerRepository = $customerRepository;
    }

    /**
     * @param CustomerSearchResultsInterface $customerCollection
     * @return array
     * @throws InputException
     * @throws InputMismatchException
     * @throws LocalizedException
     */
    public function syncCustomers(CustomerSearchResultsInterface $customerCollection): array
    {
        $count = 0;
        $result = [];
        foreach ($customerCollection->getItems() as $customer) {
            $fcResponse = $this->getFcCustomerByEmail($customer->getEmail());
            $fcCustomer = $this->json->unserialize($fcResponse);
            if ($fcCustomer['paging_info']['total'] == 1 && count($fcCustomer['data']) == 1) {
                $fcCustomerData = $fcCustomer['data'][0];
                $fcCustomerId = $fcCustomerData['id'];

                $customer->setData('fc_customer_id', $fcCustomerId);
                $this->customerRepository->save($customer);

                $count++;
            } else {
                $result['errors'][] = __('Unable to fetch fulfillment crowd customer id for %1', $customer->getEmail());
            }
        }

        $result['count'] = $count;
        return $result;
    }

    /**
     * @param string $customerEmail
     * @return bool|string
     */
    public function getFcCustomerByEmail(string $customerEmail): bool|string
    {
        $params = [
            'search_term' => $customerEmail
        ];
        return $this->getApiCall(self::ENDPOINT_CUSTOMERS, $params);
    }


    /**
     * Get Fulfillment Crowd Customer Details using Fulfillment Crowd Customer ID
     *
     * @param int $fcCustomerId
     * @return bool|string
     */
    public function getCustomerByFulfilmentCrowdCustomerId(int $fcCustomerId): bool|string
    {
        return $this->getApiCall(self::ENDPOINT_CUSTOMERS . '/' . $fcCustomerId);
    }

    /**
     * Create Fulfillment Crowd Customer using Customer Details.
     *
     * @param $order
     * @return mixed
     *
     * @throws InputException
     * @throws InputMismatchException
     * @throws LocalizedException
     * @throws Exception
     */
    public function createFcCustomer($order): mixed
    {
        $customer = $order->getCustomer();
        $customerAddress = $order->getBillingAddress();
        if ($customer && $customer->getDefaultBilling()) {
            $customerAddress = $customer->getDefaultBillingAddress();
        }

        $params = [
            'address_line1' => $customerAddress->getStreet()[0],
            'postcode' => $customerAddress->getPostcode(),
            'country_id' => $this->getFcCountryId($customerAddress->getCountryId()),
            'organisation_name' => $customerAddress->getCompany(),
            'phone' => $customerAddress->getTelephone()
        ];

        $params['contact_name'] = $order->getCustomerFirstname() . ' ' . $order->getCustomerLastname();
        $params['email'] = $order->getCustomerEmail();

        if ($customer) {
            $params['contact_name'] = $customer->getFirstname() . ' ' . $customer->getLastname();
            $params['email'] = $customer->getEmail();
        }

        $response = $this->createFcCustomerApi($params);
        $fcCustomerId = $response['id'];

        if ($customer) {
            $customer->setCustomAttribute('fc_customer_id', $fcCustomerId);
            $this->customerRepository->save($customer);
        }

        return $fcCustomerId;
    }

    /**
     * @param OrderInterface $order
     * @return mixed
     * @throws Exception
     */
    protected function createFcCustomerByOrder(OrderInterface $order): mixed
    {
        $countryCode = $order->getShippingAddress()->getCountryId();

        $fcCountryCodes = [
            'UK' => 417,
            'IM' => 1115,
            'JE' => 299,
            'GG' => 280
        ];

        $customerAddress = $order->getShippingAddress();
        $params = [
            'address_line1' => $customerAddress->getStreet()[0],
            'postcode' => $customerAddress->getPostcode(),
            'country_id' => $fcCountryCodes[$countryCode] ?? 417,
            'organisation_name' => $customerAddress->getCompany(),
            'phone' => $customerAddress->getTelephone()
        ];

        $params['contact_name'] = $order->getCustomerFirstname() . ' ' . $order->getCustomerLastname();
        $params['email'] = $order->getCustomerEmail();

        $response = $this->createFcCustomerApi($params);
        return $response['id'];
    }

    /**
     * Create Fulfillment Crowd Customer using Order Customer Data.
     *
     * @param OrderInterface $order
     * @return mixed
     * @throws InputException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @throws InputMismatchException
     */
    public function getFcCustomerByOrder(OrderInterface $order): mixed
    {
        $customerEmail = $order->getCustomerEmail();
        $params['search_term'] = $customerEmail;
        $response = $this->searchFcCustomerByEmailApi($params);

        if (isset($response['paging_info']['total']) && !empty($response['data'])) {
            $fcCustomerId = $response['data'][0]['id'];
            if ($order->getCustomerId()) {
                $customer = $this->customerRepository->getById($order->getCustomerId());
                $customer->setCustomAttribute('fc_customer_id', $fcCustomerId);
                $this->customerRepository->save($customer);
            }
            return $fcCustomerId;
        } else {
            if ($order->getCustomerId()) {
                $fcCustomerId = $this->createFcCustomer($order);
            } else {
                $fcCustomerId = $this->createFcCustomerByOrder($order);
            }

        }

        return $fcCustomerId;
    }

    /**
     * @param string|null $countryId
     * @return mixed
     */
    protected function getFcCountryId(?string $countryId): mixed
    {
        $fcCountryCollection = $this->fcCountryCollectionFactory->create()
            ->addFieldToFilter('country_code', $countryId);

        return $fcCountryCollection->getFirstItem()->getExternalCountryId();
    }
}
