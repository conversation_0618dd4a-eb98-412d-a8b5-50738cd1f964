<?php
declare(strict_types=1);

namespace MadHat\FulfilmentCrowd\Console\Command;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\App\State;
use Magento\Framework\Console\Cli;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\Exception\LocalizedException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class TruncateCountries extends Command
{
    /**
     * @var State
     */
    protected State $appState;

    /**
     * @var AdapterInterface
     */
    protected AdapterInterface $connection;

    /**
     * @param State $appState
     * @param ResourceConnection $resource
     * @param string|null $name
     */
    public function __construct(
        State $appState,
        ResourceConnection $resource,
        string $name = null
    ) {
        parent::__construct($name);
        $this->appState = $appState;
        $this->connection = $resource->getConnection();
    }

    /**
     * @inheritdoc
     */
    protected function configure(): void
    {
        $this->setName("madhat:fulfilment:truncate-countries");
        $this->setDescription("Remove Fulfillment Crowd Countries");
        parent::configure();
    }

    /**
     * Fetch Fulfillment Crowd Product ID for products using Fulfillment Crowd API
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws LocalizedException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->appState->setAreaCode('adminhtml');

        $table = $this->connection->getTableName('MadHat_FulfilmentCrowd_country');
        $this->connection->truncateTable($table);

        $output->writeln(__("Fulfillment Crowd Countries data truncated"));
        return Cli::RETURN_SUCCESS;
    }
}
