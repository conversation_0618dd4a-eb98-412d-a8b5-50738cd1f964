<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface MadHatMappingRepositoryInterface
{

    /**
     * Save MadHatMapping
     *
     * @param \MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingInterface $madHatMapping
     * @return \MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingInterface $madHatMapping
    );

    /**
     * Retrieve MadHatMapping
     *
     * @param string $madHatMappingId
     * @return \MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($madHatMappingId);

    /**
     * Retrieve MadHatMapping matching the specified criteria.
     *
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete MadHatMapping
     *
     * @param \MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingInterface $madHatMapping
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingInterface $madHatMapping
    );

    /**
     * Delete MadHatMapping by ID
     *
     * @param string $madHatMappingId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($madHatMappingId);
}
