<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Api\Data;

interface DoptionsInterface
{
    public const TYPE = 'type';
    public const LABEL = 'label';
    public const DESCRIPTION = 'description';
    public const DOPTIONS_ID = 'doptions_id';
    public const VALUE = 'value';

    /**
     * Get doptions_id
     *
     * @return string|null
     */
    public function getDoptionsId();

    /**
     * Set doptions_id
     *
     * @param string $doptionsId
     * @return \MadHat\SiteIntegrationProducts\Doptions\Api\Data\DoptionsInterface
     */
    public function setDoptionsId($doptionsId);

    /**
     * Get value
     *
     * @return string|null
     */
    public function getValue();

    /**
     * Set value
     *
     * @param string $value
     * @return \MadHat\SiteIntegrationProducts\Doptions\Api\Data\DoptionsInterface
     */
    public function setValue($value);

    /**
     * Get label
     *
     * @return string|null
     */
    public function getLabel();

    /**
     * Set label
     *
     * @param string $label
     * @return \MadHat\SiteIntegrationProducts\Doptions\Api\Data\DoptionsInterface
     */
    public function setLabel($label);

    /**
     * Get type
     *
     * @return string|null
     */
    public function getType();

    /**
     * Set type
     *
     * @param string $type
     * @return \MadHat\SiteIntegrationProducts\Doptions\Api\Data\DoptionsInterface
     */
    public function setType($type);

    /**
     * Get description
     *
     * @return string|null
     */
    public function getDescription();

    /**
     * Set description
     *
     * @param string $description
     * @return \MadHat\SiteIntegrationProducts\Doptions\Api\Data\DoptionsInterface
     */
    public function setDescription($description);
}
