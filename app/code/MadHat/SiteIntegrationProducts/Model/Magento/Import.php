<?php

namespace MadHat\SiteIntegrationProducts\Model\Magento;

use Magento\Framework\Exception\LocalizedException;
use Magento\ImportExport\Model\Import as CoreImport;

class Import extends CoreImport
{
    /**
     * Add CSV source file to Import Source
     *
     * @return CoreImport\AbstractSource
     * @throws LocalizedException
     */
    public function uploadFileAndGetSource()
    {
        $sourceFile = "/app/var/importexport/catalog_product.csv";
        try {
            $source = $this->_getSourceAdapter($sourceFile);
        } catch (\Exception $e) {
            throw new LocalizedException(__($e->getMessage()));
        }

        return $source;
    }
}
