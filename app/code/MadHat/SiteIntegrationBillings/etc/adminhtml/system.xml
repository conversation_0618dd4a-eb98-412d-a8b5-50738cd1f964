<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="madhat_siteintegrationbillings" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1" translate="label">
            <label>MadHat Site Integration Billings</label>
            <tab>madhat</tab>
            <resource>MadHat_SiteIntegrationBillings::config</resource>
            <group id="general" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Billings / Invoices</label>
                <field id="invoice_folder" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Invoice Folder</label>
                    <comment>Set the base folder for invoice storage. (Default: billings)</comment>
                </field>
                <field id="directory_type" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Directory Type</label>
                    <source_model>MadHat\SiteIntegrationBillings\Model\Config\Source\DirectoryType</source_model>
                    <comment>Select the base directory (var, media, pub, root)</comment>
                </field>
            </group>
        </section>
    </system>
</config>
