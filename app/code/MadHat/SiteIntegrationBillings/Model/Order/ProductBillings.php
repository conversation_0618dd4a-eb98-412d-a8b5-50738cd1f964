<?php

namespace MadHat\SiteIntegrationBillings\Model\Order;

use MadHat\SiteIntegrationBillings\Model\Api\Billings as BillingsApi;
use Magento\Framework\Exception\LocalizedException;

class ProductBillings
{
    public function __construct(
        BillingsApi $billingsApi
    )
    {
        $this->billingsApi = $billingsApi;
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function DownloadInvoice(\Magento\Sales\Api\Data\OrderInterface $order)
    {
        // Download pdf Invoice
        $billings = $this->billingsApi->getBillingsByOrder($order);
        foreach ($billings as $billing) {
            $this->billingsApi->saveBilling($billing, $order);
        }
    }

    /**
     * @param array $orderStatusData
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return void
     * @throws LocalizedException
     */
    public function DownloadInvoiceBySiteResponce($orderStatusData, $order)
    {
        $billings = $this->billingsApi->getBillingsFromStatusData($orderStatusData);

        foreach ($billings as $billing) {
            $this->billingsApi->saveBilling($billing, $order);
        }
    }


}
