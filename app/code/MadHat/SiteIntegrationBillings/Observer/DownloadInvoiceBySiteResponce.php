<?php

namespace MadHat\SiteIntegrationBillings\Observer;

use \Magento\Shipping\Model\ShipmentNotifier;
use Magento\Sales\Api\OrderRepositoryInterface;
use MadHat\SiteIntegrationBillings\Model\Order\ProductBillings;
use Magento\Framework\Event\Observer;
use Psr\Log\LoggerInterface;

class DownloadInvoiceBySiteResponce implements \Magento\Framework\Event\ObserverInterface
{
    private ShipmentNotifier $shipmentNotifier;
    private OrderRepositoryInterface $orderRepository;
    private ProductBillings $productBillings;
    private LoggerInterface $logger;

    /**
     * @param ShipmentNotifier $shipmentNotifier
     * @param OrderRepositoryInterface $orderRepository
     * @param ProductBillings $productBillings
     * @param LoggerInterface $logger
     */
    public function __construct(
        ShipmentNotifier $shipmentNotifier,
        OrderRepositoryInterface $orderRepository,
        ProductBillings $productBillings,
        LoggerInterface $logger
    ) {
        $this->shipmentNotifier = $shipmentNotifier;
        $this->orderRepository = $orderRepository;
        $this->productBillings = $productBillings;
        $this->logger = $logger;
    }

    public function execute(Observer $observer)
    {
        $orderId = $observer->getEvent()->getOrderId();
        $jsonSITEdata = $observer->getEvent()->getResponse();

        try {
            $order = $this->orderRepository->get($orderId);
            if(!empty($orderId)) {
                $this->logger->debug(__('%1 order entity_id: %2; SITE data: %3',
                        $orderId, print_r($jsonSITEdata, true), print_r($jsonSITEdata,true))
                );
                $this->productBillings->DownloadInvoiceBySiteResponce($jsonSITEdata, $order);
                if ($order->hasShipments()) {
                    $shipments = $order->getShipmentsCollection();
                    foreach ($shipments as $shipment) {
                        if($this->shipmentNotifier->notify($shipment)) {
                            $this->logger->debug(__('%1. %2 Shipment send for ODRED ID %3 Shipment ID: %4', __LINE__, __CLASS__, $order->getEntityId(), $shipment->getId()));
                        } else {
                            $this->logger->debug(__('%1. %2 Shipment can not send for ODRED ID %3 Shipment ID: %4', __LINE__, __CLASS__, $shipment->getId(), $order->getEntityId(), $shipment->getId()));
                        }
                    }
                } else {

                }
            }
        } catch (\Exception $e) {
            $this->logger->error(__('%1, %2 ERROR: %3',__LINE__, __CLASS__, $e->getMessage()));
        }

    }
}
