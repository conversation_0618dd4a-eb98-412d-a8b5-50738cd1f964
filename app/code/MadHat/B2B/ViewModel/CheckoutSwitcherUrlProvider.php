<?php

namespace MadHat\B2B\ViewModel;

use Magento\Store\ViewModel\SwitcherUrlProvider;

use Magento\Framework\App\ActionInterface;
use Magento\Framework\Url\EncoderInterface;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;
class CheckoutSwitcherUrlProvider extends SwitcherUrlProvider
{
    /**
     * @var EncoderInterface
     */
    private $encoder;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var UrlInterface
     */
    private $urlBuilder;

    /**
     * @param EncoderInterface $encoder
     * @param StoreManagerInterface $storeManager
     * @param UrlInterface $urlBuilder
     */
    public function __construct(
        EncoderInterface $encoder,
        StoreManagerInterface $storeManager,
        UrlInterface $urlBuilder
    ) {
        $this->encoder = $encoder;
        $this->storeManager = $storeManager;
        $this->urlBuilder = $urlBuilder;
    }

    /**
     * New method similar to getTargetStoreRedirectUrl
     *
     * @param int $storeId
     * @return string
     */
    public function getTargetStoreRedirectUrlCheckout(Store $store): string
    {
        $checkoutCartUrl = $store->getBaseUrl();
        return $this->urlBuilder->getUrl(
            'stores/store/redirect',
            [
                '___store' => $store->getCode(),
                '___from_store' => $this->storeManager->getStore()->getCode(),
                ActionInterface::PARAM_NAME_URL_ENCODED => $this->encoder->encode($checkoutCartUrl."checkout/cart/"),
            ]
        );
    }
}
