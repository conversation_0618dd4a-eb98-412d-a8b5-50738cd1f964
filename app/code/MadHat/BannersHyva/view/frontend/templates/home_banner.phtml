<?php
declare(strict_types=1);

/**
 *  @codingStandardsIgnoreFile
 **/
/** @var \MadHat\Banners\Block\Banners $block */
use MadHat\Banners\Model\Source\BannerPosition;
use Hyva\Theme\ViewModel\Slider;

/** @var Slider $sliderViewModel */
$sliderViewModel = $viewModels->require(Slider::class);
$bannerId = [BannerPosition::HOME_PAGE_MAIN_BANNER];
$banners  = $block->getFrontBanners($bannerId);
?>
<?php $items = []; ?>
<?php foreach($banners as $banner) : ?>
    <?php $bannerImage = $banner->getBannerimage(); ?>
    <?php $mobileBannerImage = $banner->getMobileBannerImage(); ?>
    <?php $items[] = [
            'image_url' => $bannerImage,
            'mobile_image_url' => ($mobileBannerImage) ? $mobileBannerImage : $bannerImage,
            'image_width'=> 1280,
            'image_height' => 853,
            'text_small' => $banner->getTextSmall() ? $banner->getTextSmall() : '',
            'text_large' => $banner->getTextLarge() ? $banner->getTextLarge() : '',
            'url' => $block->getUrl($banner->getLink()),
        ];
    ?>
<?php endforeach; ?>

<?php
$sliderShowArrows = $block->getShowArrows() !== null ? $block->getShowArrows() : "end";
$sliderShowDots = $block->getShowDots() !== null ? (bool) $block->getShowDots() : 'true';
$loadFirstEager = (bool) $block->getLoadFirstEager();
?>

<div class="banner-position-<?= isset($banner) ? $banner->getBannerPosition() : '1' ?>">
    <?=
    $sliderViewModel
        ->getSliderForItems(
            'MadHat_BannersHyva::ui-slider/slider-item.phtml',
            $items,
            'MadHat_BannersHyva::ui-slider/slider-php.phtml'
        )
        ->setShowArrows($sliderShowArrows)
        ->setShowDots($sliderShowDots)
        ->setLoadFirstEager($loadFirstEager)
        ->toHtml()
    ?>
</div>
