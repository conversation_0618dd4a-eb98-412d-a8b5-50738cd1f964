<?php

namespace MadHat\InventoryImport\Plugin\Model;

use Magento\CatalogInventory\Api\Data\StockItemInterface;
use Magento\Framework\DataObject\Factory as ObjectFactory;
use Magento\Framework\Locale\FormatInterface;
use MadHat\InventoryImport\Plugin\AbstractPlugin;
use MadHat\InventoryImport\Helper\ProductStatusConfig;
use MadHat\InventoryImport\Model\Logger\ProductStatusLogger;

class StockStateProvider extends AbstractPlugin
{
    /**
     * @var ObjectFactory
     */
    protected $objectFactory;

    /**
     * @var FormatInterface
     */
    protected $localeFormat;

    public function __construct(
        ProductStatusConfig $productStatusConfig,
        ProductStatusLogger $productStatusLogger,
        ObjectFactory $objectFactory,
        FormatInterface $localeFormat
    ) {
        parent::__construct(
            $productStatusConfig,
            $productStatusLogger
        );
        $this->objectFactory = $objectFactory;
        $this->localeFormat = $localeFormat;
    }
    public function aroundCheckQuoteItemQty(
        \Magento\CatalogInventory\Model\StockStateProvider $stockStateProvider,
        callable $proceed,
        StockItemInterface $stockItem,
        $qty,
        $summaryQty,
        $origQty = 0
    ) {
        if ($this->isInventoryOnStatus() && $this->isNoManageStock()) {
            $result = $this->objectFactory->create();
            $result->setHasError(false);

            $qty = $this->getNumber($qty);

            /**
             * Check quantity type
             */
            $result->setItemIsQtyDecimal(false);
            $result->setHasQtyOptionUpdate(true);
            $qty = (int) $qty ?: 1;
            /**
             * Adding stock data to quote item
             */
            $result->setItemQty($qty);
            $result->setOrigQty((int)$this->getNumber($origQty) ?: 1);
            return  $result;
        }
        return  $proceed($stockItem, $qty, $summaryQty, $origQty);
    }
    /**
     * Get numeric qty
     *
     * @param string|float|int|null $qty
     * @return float|null
     */
    protected function getNumber($qty)
    {
        if (!is_numeric($qty)) {
            $qty = $this->localeFormat->getNumber($qty);
            return $qty;
        }
        return $qty;
    }
}
