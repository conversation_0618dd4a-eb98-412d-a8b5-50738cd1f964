<?php

namespace MadHat\InventoryImport\Plugin\Reservation;

use Magento\InventoryReservationsApi\Model\GetReservationsQuantityInterface;
use MadHat\InventoryImport\Helper\ProductStatusConfig;

class GetReservation
{
    private $productStatusConfigHelper;

    public function __construct(
        ProductStatusConfig $productStatusConfigHelper
    ) {
        $this->productStatusConfigHelper = $productStatusConfigHelper;
    }

    public function aroundExecute(
        GetReservationsQuantityInterface $subject,
        callable $proceed,
        string $sku,
        int $stockId
    ) {
        if ($this->productStatusConfigHelper->ignoreReservation()) {
            return 0;
        }

        return $proceed($sku, $stockId);
    }
}
