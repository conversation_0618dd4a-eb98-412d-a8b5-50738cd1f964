<?php

namespace MadHat\SiteIntegrationCancelorder\Api\Data;

interface CancelorderDataInterface
{
    /**
     * @return int|null
     */
    public function getVismaOrderNo();

    /**
     * @param int|null $vismaOrderNo
     * @return void
     */
    public function setVismaOrderNo(?int $vismaOrderNo);

    /**
     * @return string|null
     */
    public function getWebOrderNo();

    /**
     * @param string|null $webOrderNo
     * @return void
     */
    public function setWebOrderNo(?string $webOrderNo);

    /**
     * @return string|null
     */
    public function getExternalOrderNo();

    /**
     * @param string|null $externalOrderNo
     * @return void
     */
    public function setExternalOrderNo(?string $externalOrderNo);

    /**
     * @return int|null
     */
    public function getPortal();

    /**
     * @param int|null $portal
     * @return void
     */
    public function setPortal(?int $portal);

    /**
     * @return int|null
     */
    public function getStatus();

    /**
     * @param int|null $status
     * @return void
     */
    public function setStatus(?int $status);

    /**
     * @return string|null
     */
    public function getStatusText();

    /**
     * @param string|null $statusText
     * @return void
     */
    public function setStatusText(?string $statusText);

    /**
     * @return int|null
     */
    public function getOrderType();

    /**
     * @param int|null $orderType
     * @return void
     */
    public function setOrderType(?int $orderType);

    /**
     * @return string|null
     */
    public function getOrderTypeText();

    /**
     * @param string|null $orderTypeText
     * @return void
     */
    public function setOrderTypeText(?string $orderTypeText);

    /**
     * @return float|null
     */
    public function getAmountGross();

    /**
     * @param float|null $amountGross
     * @return void
     */
    public function setAmountGross(?float $amountGross);

    /**
     * @return float|null
     */
    public function getAmountNet();

    /**
     * @param float|null $amountNet
     * @return void
     */
    public function setAmountNet(?float $amountNet);

    /**
     * @return float|null
     */
    public function getCostPriceSumNet();

    /**
     * @param float|null $costPriceSumNet
     * @return void
     */
    public function setCostPriceSumNet(?float $costPriceSumNet);

    /**
     * @return int|null
     */
    public function getSeller();

    /**
     * @param int|null $seller
     * @return void
     */
    public function setSeller(?int $seller);

    /**
     * @return string|null
     */
    public function getOrderDate();

    /**
     * @param string|null $orderDate
     * @return void
     */
    public function setOrderDate(?string $orderDate);

    /**
     * @return int|null
     */
    public function getChangeDate();

    /**
     * @param int|null $changeDate
     * @return void
     */
    public function setChangeDate(?int $changeDate);

    /**
     * @return \MadHat\SiteIntegrationCancelorder\Api\Data\PaymentMethodInterface|null
     */
    public function getPaymentMethod();

    /**
     * @param \MadHat\SiteIntegrationCancelorder\Api\Data\PaymentMethodInterface|null $paymentMethod
     * @return void
     */
    public function setPaymentMethod(?PaymentMethodInterface $paymentMethod);

    /**
     * @return string|null
     */
    public function getCurrencyISO();

    /**
     * @param string|null $currencyISO
     * @return void
     */
    public function setCurrencyISO(?string $currencyISO);

    /**
     * @return string|null
     */
    public function getCountryISO();

    /**
     * @param string|null $countryISO
     * @return void
     */
    public function setCountryISO(?string $countryISO);

    /**
     * @return string|null
     */
    public function getShippingCountryISO();

    /**
     * @param string|null $shippingCountryISO
     * @return void
     */
    public function setShippingCountryISO(?string $shippingCountryISO);

    /**
     * @return int|null
     */
    public function getShipmentStatus();

    /**
     * @param int|null $shipmentStatus
     * @return void
     */
    public function setShipmentStatus(?int $shipmentStatus);

    /**
     * @return int|null
     */
    public function getFulfilmentWorkflow();

    /**
     * @param int|null $fulfilmentWorkflow
     * @return void
     */
    public function setFulfilmentWorkflow(?int $fulfilmentWorkflow);

    /**
     * @return \MadHat\SiteIntegrationCancelorder\Api\Data\OrderRowsDataInterface[]|null
     */
    public function getOrderRowsData();

    /**
     * @param \MadHat\SiteIntegrationCancelorder\Api\Data\OrderRowsDataInterface[]|null $orderRowsData
     * @return void
     */
    public function setOrderRowsData(?array $orderRowsData);

    /**
     * @return \MadHat\SiteIntegrationCancelorder\Api\Data\ConsignmentsInterface[]|null
     */
    public function getConsignments();

    /**
     * @param \MadHat\SiteIntegrationCancelorder\Api\Data\ConsignmentsInterface[]|null $consignments
     * @return void
     */
    public function setConsignments(?array $consignments);

    /**
     * @return \MadHat\SiteIntegrationCancelorder\Api\Data\ProductShipmentsInterface[]|null
     */
    public function getProductShipments();

    /**
     * @param \MadHat\SiteIntegrationCancelorder\Api\Data\ProductShipmentsInterface[]|null $productShipments
     * @return void
     */
    public function setProductShipments(?array $productShipments);

    /**
     * @return \MadHat\SiteIntegrationCancelorder\Api\Data\BillingsInterface[]|null
     */
    public function getBillings();

    /**
     * @param \MadHat\SiteIntegrationCancelorder\Api\Data\BillingsInterface[]|null $billings
     * @return void
     */
    public function setBillings(?array $billings);
}
