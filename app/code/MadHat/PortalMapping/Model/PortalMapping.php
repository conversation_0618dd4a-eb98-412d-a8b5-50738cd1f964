<?php
/**
 * MadHat_PortalMapping extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MadHat License.
 *
 * @category  MadHat
 * @package   MadHat_PortalMapping
 * @copyright Copyright (c) 2021
 **/

namespace MadHat\PortalMapping\Model;

use MadHat\PortalMapping\Api\Data\PortalMappingInterface;

class PortalMapping extends \Magento\Framework\Model\AbstractModel implements PortalMappingInterface
{
    /**
     * CMS page cache tag.
     */
    const CACHE_TAG = 'madhat_portal_mapping';

    /**
     * @var string
     */
    protected $_cacheTag = 'madhat_portal_mapping';

    /**
     * Prefix of model events names.
     *
     * @var string
     */
    protected $_eventPrefix = 'madhat_portal_mapping';

    /**
     * Initialize resource model.
     */
    protected function _construct()
    {
        $this->_init('MadHat\PortalMapping\Model\ResourceModel\PortalMapping');
    }
    /**
     * Get EntityId.
     *
     * @return int
     */
    public function getEntityId()
    {
        return $this->getData(self::ENTITY_ID);
    }

    /**
     * Set EntityId.
     */
    public function setEntityId($entityId)
    {
        return $this->setData(self::ENTITY_ID, $entityId);
    }

    /**
     * Get PortalId.
     *
     * @return array|int|mixed|null
     */
    public function getPortalId()
    {
        return $this->getData(self::PORTAL_ID);
    }

    /**
     * Set PortalId.
     */
    public function setPortalId($portalId)
    {
        return $this->setData(self::PORTAL_ID, $portalId);
    }

    /**
     * Get getStoreId.
     *
     * @return array|int|mixed|null
     */
    public function getStoreId()
    {
        return $this->getData(self::STORE_ID);
    }

    /**
     * Set StoreId.
     */
    public function setStoreId($storeId)
    {
        return $this->setData(self::STORE_ID, $storeId);
    }

    /**
     * Get CurrencyCode.
     *
     * @return array|int|mixed|null
     */
    public function getCurrencyCode()
    {
        return $this->getData(self::CURRENCY_CODE);
    }

    /**
     * Set CurrencyCode.
     *
     * @param $currencyCode
     * @return Grid
     */
    public function setCurrencyCode($currencyCode)
    {
        return $this->setData(self::CURRENCY_CODE, $currencyCode);
    }

    /**
     * Get UpdateTime.
     *
     * @return array|\Magento\Framework\Stdlib\DateTime|mixed|null
     */
    public function getUpdatedAt()
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * Set UpdatedAt
     *
     * @param $updatedAt
     * @return Grid
     */
    public function setUpdatedAt($updatedAt)
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * Get CreatedAt.
     *
     * @return array|\Magento\Framework\Stdlib\DateTime|mixed|null
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set CreatedAt.
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }
}
