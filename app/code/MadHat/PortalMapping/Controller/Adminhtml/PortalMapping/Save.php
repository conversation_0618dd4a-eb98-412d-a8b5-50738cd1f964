<?php
/**
 * MadHat_PortalMapping extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MadHat License.
 *
 * @category  MadHat
 * @package   MadHat_PortalMapping
 * @copyright Copyright (c) 2021
 **/

namespace MadHat\PortalMapping\Controller\Adminhtml\PortalMapping;

class Save extends \Magento\Backend\App\Action
{
    /**
     * @var \MadHat\PortalMappingModel\PortalMappingFactory
     */
    var $portalMappingFactory;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \MadHat\PortalMapping\Model\PortalMappingFactory $portalMappingFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \MadHat\PortalMapping\Model\PortalMappingFactory $portalMappingFactory
    ) {
        parent::__construct($context);
        $this->portalMappingFactory = $portalMappingFactory;
    }

    /**
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();
        if (!$data) {
            $this->_redirect('portalmapping/portalmapping/addrow');
            return;
        }
        try {
            $rowData = $this->portalMappingFactory->create();
            $rowData->setData($data);
            if (isset($data['id'])) {
                $rowData->setEntityId($data['id']);
            }
            $rowData->save();
            $this->messageManager->addSuccess(__('Portal information has been successfully saved.'));
        } catch (\Exception $e) {
            $this->messageManager->addError(__($e->getMessage()));
        }
        $this->_redirect('portalmapping/portalmapping/index');
    }

    /**
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('MadHat_PortalMapping::save');
    }
}
