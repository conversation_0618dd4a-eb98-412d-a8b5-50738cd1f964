<?php

namespace MadHat\ImportReview\Controller\Adminhtml\Import;

use Magento\Backend\App\Action;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\File\Csv;
use MadHat\ImportReview\Model\ImportReview;

class Save extends Action
{
    protected $csv;
    protected $importReview;

    public function __construct(
        Action\Context $context,
        Csv $csv,
        ImportReview $importReview
    ) {
        parent::__construct($context);
        $this->csv = $csv;
        $this->importReview = $importReview;
    }

    public function execute()
    {
        $file = $this->getRequest()->getFiles('import_reviews_file');

        if ($file && isset($file['tmp_name'])) {
            $filePath = $file['tmp_name'];
            $data = $this->csv->getData($filePath);
            $this->importReview->importReviews($data);

            $this->messageManager->addSuccessMessage(__('Customer reviews have been imported.'));
        } else {
            $this->messageManager->addErrorMessage(__('Please upload a valid CSV file.'));
        }

        return $this->_redirect('*/*/index');
    }
}
