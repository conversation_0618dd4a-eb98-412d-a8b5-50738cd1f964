<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi;

/**
 * Order Status Manager
 *
 * This class manages order status updates in Magento based on status changes
 * received from the SITE/ERP system. It handles the three main order statuses
 * and updates the corresponding flags in the madhat_order_info table.
 *
 * Key Responsibilities:
 * - Process "Picked" status updates from SITE
 * - Process "Invoiced" status updates from SITE
 * - Process "Captured" payment status updates
 * - Update madhat_order_info table with status flags
 * - Handle special pricing adjustments (ROUND product)
 * - Refresh order grid display after updates
 * - Maintain status history and audit trail
 *
 * Status Types Handled:
 *
 * 1. Picked Status:
 *    - Indicates items are ready for shipment
 *    - May include item modifications from warehouse
 *    - Processes ROUND pricing adjustments
 *    - Sets site_is_picked flag to true
 *
 * 2. Invoiced Status:
 *    - Indicates order has been billed
 *    - Contains final pricing and billing information
 *    - Sets site_is_invoiced flag to true
 *    - Triggers billing-related events
 *
 * 3. Captured Status:
 *    - Indicates payment has been captured
 *    - Used for payment transaction tracking
 *    - Sets site_is_captured flag to true
 *    - Confirms payment processing completion
 *
 * Database Updates:
 * - Updates madhat_order_info table with status flags
 * - Maintains referential integrity with orders
 * - Handles concurrent update scenarios
 * - Refreshes order grid for admin visibility
 *
 * Special Processing:
 * - ROUND product: Handles rounding adjustments from SITE
 * - Status validation: Ensures status changes are valid
 * - Error handling: Manages database update failures
 *
 * @see MadHat\OrderIntegration\Api\MadhatOrderInfoRepositoryInterface
 * @see MadHat\OrderIntegration\Model\OrderGridRefresher
 */

use MadHat\OrderIntegration\Api\MadhatOrderInfoRepositoryInterface;
use MadHat\OrderIntegration\Model\OrderGridRefresher;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class OrderStatusManager
{
    protected const PICKED_STATUS = 'Picked';
    protected const INVOICED_STATUS = 'Invoiced';

    /**
     * @var MadhatOrderInfoRepositoryInterface
     */
    protected MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository;

    /**
     * @var OrderGridRefresher
     */
    protected OrderGridRefresher $orderGridRefresher;

    public function __construct(
        MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository,
        OrderGridRefresher $orderGridRefresher
    ) {
        $this->madhatOrderInfoRepository = $madhatOrderInfoRepository;
        $this->orderGridRefresher = $orderGridRefresher;
    }

    /**
     * Save Picked Status
     *
     * @param int $magentoOrderId
     * @param mixed $response
     * @return void
     * @throws CouldNotSaveException
     * @throws NoSuchEntityException
     */
    public function savePickedStatus(int $magentoOrderId, mixed $response): void
    {
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId($magentoOrderId);

        if (isset($response['OrderStatusData']['Status']) && $response['OrderStatusData']['Status'] == self::PICKED_STATUS) {
            $madhatOrderInfo->setSiteIsPicked(true);
            foreach ($response['OrderStatusData']['OrderRowsData'] as $orderRow) {
                if ($orderRow['ProductNo'] == 'ROUND') {
                    $madhatOrderInfo->setSiteRound((float)$orderRow['PriceGross']);
                }
            }
        }

        $this->madhatOrderInfoRepository->save($madhatOrderInfo);
        $this->orderGridRefresher->refresh($magentoOrderId);
    }

    /**
     * Save Invoiced Status
     *
     * @param int $magentoOrderId
     * @param mixed $response
     * @return void
     * @throws CouldNotSaveException
     * @throws NoSuchEntityException
     */
    public function saveInvoicedStatus(int $magentoOrderId, mixed $response): void
    {
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId((int)$magentoOrderId);

        if (isset($response['OrderStatusData']['Status']) && $response['OrderStatusData']['Status'] == self::INVOICED_STATUS) {
            $madhatOrderInfo->setSiteIsInvoiced(true);
        }

        $this->madhatOrderInfoRepository->save($madhatOrderInfo);
        $this->orderGridRefresher->refresh($magentoOrderId);
    }

    /**
     * Save Captured Status
     *
     * @param int $magentoOrderId
     * @param mixed $response
     * @return void
     * @throws CouldNotSaveException
     * @throws NoSuchEntityException
     */
    public function saveCapturedStatus(int $magentoOrderId, mixed $response): void
    {
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId($magentoOrderId);

        if (isset($response['ResponseResult']) && $response['ResponseResult']['Code'] == 'SUCCESS') {
            $madhatOrderInfo->setSiteIsCaptured(true);
        }

        $this->madhatOrderInfoRepository->save($madhatOrderInfo);
    }
}
