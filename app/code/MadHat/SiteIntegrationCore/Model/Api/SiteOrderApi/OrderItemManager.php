<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi;

/**
 * Order Item Manager
 *
 * This class handles complex order item modifications during the synchronization
 * process from SITE/ERP back to Magento. It manages adding, removing, and
 * modifying order items based on changes made in the SITE system.
 *
 * Key Responsibilities:
 * - Compare Magento order items with SITE order data
 * - Add new items that were added in SITE
 * - Remove items that were removed in SITE
 * - Modify existing items (quantities, prices, discounts)
 * - Update order totals based on SITE calculations
 * - Handle currency conversions for multi-currency orders
 * - Manage discount calculations and applications
 * - Process configurable vs simple product relationships
 * - Update database records directly for performance
 *
 * Item Modification Process:
 * 1. Compare current Magento items with SITE data
 * 2. Identify items to add, remove, or modify
 * 3. Process removals first to avoid conflicts
 * 4. Add new items with proper pricing and configuration
 * 5. Modify existing items (qty, price, discount)
 * 6. Fix discount calculations via direct database updates
 * 7. Set original prices for proper Magento calculations
 * 8. Save changes using Mirasvit Order Editor
 *
 * Order Totals Management:
 * - Updates grand total, tax amounts, and base amounts
 * - Handles currency conversion when needed
 * - Processes shipping costs and discount amounts
 * - Manages special line items (RABATT20, 8b)
 *
 * Database Operations:
 * - Direct SQL updates for performance on large orders
 * - Transactional updates to ensure data consistency
 * - Proper handling of base currency vs order currency
 * - Updates discount_percent, discount_amount fields
 * - Sets original_price and base_original_price
 *
 * Product Handling:
 * - Manages configurable product parent-child relationships
 * - Handles simple product additions and modifications
 * - Processes product SKU mappings and validations
 * - Excludes custom SITE products from synchronization
 *
 * @see DatabaseOperations For currency conversion utilities
 * @see Mirasvit\OrderEditor For order modification framework
 */

use Exception;
use MadHat\SiteIntegrationOrder\Helper\Data as SiteOrderHelper;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable as ConfigurableType;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Api\Data\OrderItemInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\OrderFactory;
use Magento\Sales\Model\OrderRepository;
use Mirasvit\OrderEditor\Section\ItemsSection;
use Psr\Log\LoggerInterface;

class OrderItemManager
{
    /**
     * @var OrderFactory
     */
    protected OrderFactory $magentoOrderFactory;

    /**
     * @var OrderRepository
     */
    protected OrderRepository $magentoOrderRepository;

    /**
     * @var SiteOrderHelper
     */
    protected SiteOrderHelper $siteOrderHelper;

    /**
     * @var ProductRepositoryInterface
     */
    protected ProductRepositoryInterface $productRepository;

    /**
     * @var ConfigurableType
     */
    protected ConfigurableType $configurableType;

    /**
     * @var ItemsSection
     */
    protected ItemsSection $itemsSection;

    /**
     * @var ResourceConnection
     */
    protected ResourceConnection $resourceConnection;

    /**
     * @var DatabaseOperations
     */
    protected DatabaseOperations $databaseOperations;

    /**
     * @var LoggerInterface
     */
    protected LoggerInterface $logger;

    public function __construct(
        OrderFactory $magentoOrderFactory,
        OrderRepository $magentoOrderRepository,
        SiteOrderHelper $siteOrderHelper,
        ProductRepositoryInterface $productRepository,
        ConfigurableType $configurableType,
        ItemsSection $itemsSection,
        ResourceConnection $resourceConnection,
        DatabaseOperations $databaseOperations,
        LoggerInterface $logger
    ) {
        $this->magentoOrderFactory = $magentoOrderFactory;
        $this->magentoOrderRepository = $magentoOrderRepository;
        $this->siteOrderHelper = $siteOrderHelper;
        $this->productRepository = $productRepository;
        $this->configurableType = $configurableType;
        $this->itemsSection = $itemsSection;
        $this->resourceConnection = $resourceConnection;
        $this->databaseOperations = $databaseOperations;
        $this->logger = $logger;
    }

    /**
     * Edit Order Items based on SITE data
     *
     * @param int $magentoOrderId
     * @param array $siteOrderData
     * @return void
     * @throws NoSuchEntityException
     * @throws Exception
     */
    public function editOrderItems(int $magentoOrderId, array $siteOrderData): void
    {
        $this->logger->debug("[Line " . __LINE__ . "] Starting editOrderItems for Magento order ID: {$magentoOrderId}");
        $order = $this->magentoOrderFactory->create()->load($magentoOrderId);
        $this->logger->debug("[Line " . __LINE__ . "] Order loaded with increment ID: {$order->getIncrementId()}");
        $websiteId = $order->getStore()->getWebsiteId();
        $this->logger->debug("[Line " . __LINE__ . "] Website ID: {$websiteId}");

        $magentoOrderItems = [];
        foreach ($order->getItems() as $orderItem) {
            if ($orderItem->getProductType() == 'configurable') {
                $this->logger->debug("[Line " . __LINE__ . "] Skipping configurable product: {$orderItem->getProduct()->getSku()}");
                continue;
            }
            $sku = $orderItem->getProduct()->getSku();
            $magentoOrderItems[] = $sku;
            $this->logger->debug("[Line " . __LINE__ . "] Added to array Magento order item: {$sku}, Qty: {$orderItem->getQtyOrdered()}, Price: {$orderItem->getPrice()}");
        }
        $this->logger->debug("[Line " . __LINE__ . "] Magento order items: " . json_encode($magentoOrderItems));

        $siteOrderItems = [];
        $customSiteProducts = $this->siteOrderHelper->getCustomSiteProducts($websiteId);
        $this->logger->debug("[Line " . __LINE__ . "] Custom site products: " . json_encode($customSiteProducts));
        $this->logger->debug("[Line " . __LINE__ . "] OrderRowsData: " . var_export($siteOrderData['OrderRowsData'], true));

        foreach ($siteOrderData['OrderRowsData'] as $item) {
            if (in_array($item['ProductNo'], $customSiteProducts)) {
                $this->logger->debug("[Line " . __LINE__ . "] Skipping custom site product: {$item['ProductNo']}");
                continue;
            }
            $siteOrderItems[] = $item['ProductNo'];
            $this->logger->debug("[Line " . __LINE__ . "] Added site order item: {$item['ProductNo']}, Qty: {$item['Quantity']}, Price: {$item['PriceNet']}");
        }
        $this->logger->debug("[Line " . __LINE__ . "] Site order items: " . json_encode($siteOrderItems));
        $this->logger->debug("[Line " . __LINE__ . "] Values for DIFF: siteOrderItems:" . var_export($siteOrderItems, true));
        $this->logger->debug("[Line " . __LINE__ . "] Values for DIFF: magentoOrderItems:" . var_export($magentoOrderItems, true));

        $productsToBeAdded = array_values(array_diff($siteOrderItems, $magentoOrderItems));
        $this->logger->debug("[Line " . __LINE__ . "] List of products to be added: " . json_encode($productsToBeAdded));

        $productsToBeRemoved = array_values(array_diff($magentoOrderItems, $siteOrderItems));
        $this->logger->debug("[Line " . __LINE__ . "] List of products to be removed: " . json_encode($productsToBeRemoved));

        $productsToBeModified = $this->getProductsToBeModified($order, $productsToBeRemoved, $siteOrderData);

        $newOrderData = [];
        $skuForFixFunctions = $this->getSkuForFixFunctions($productsToBeAdded, $productsToBeModified);
        $this->logger->debug(__('[Line: %1] %2, SKU List for fix functions: %3', __LINE__, __CLASS__, json_encode($skuForFixFunctions)));
        $newOrderData['payment'] = [];
        $newOrderData['shipping'] = [];

        if (!empty($productsToBeAdded) || !empty($productsToBeRemoved) || !empty($productsToBeModified)) {
            $this->logger->debug("[Line " . __LINE__ . "] Order items need modification. Processing changes...");

            // Remove Item
            $newOrderData = $this->removeOrderItems($order, $newOrderData, $productsToBeRemoved);
            // Add Item
            $newOrderData = $this->addOrderItems($order, $newOrderData, $productsToBeAdded, $siteOrderData);
            // Modify Item
            $newOrderData = $this->modifyOrderItems($newOrderData, $productsToBeModified);
            // Fix discounts before saving to be able to row recalculation
            $this->fixDiscountForExistProducts($order, $siteOrderData, $skuForFixFunctions);

            // Save data
            if (!empty($newOrderData)) {
                $this->logger->debug("[Line " . __LINE__ . "] Saving new order data: " . json_encode($newOrderData));
                try {
                    $this->itemsSection->save($order, $newOrderData);
                    $this->setOriginalPricesForOrderItems($order, $skuForFixFunctions);
                    $this->logger->debug("[Line " . __LINE__ . "] Order items successfully updated for order ID: {$magentoOrderId}");
                } catch (\Exception $e) {
                    $this->logger->error("[Line " . __LINE__ . "] Error saving order items for order ID {$magentoOrderId}: " . $e->getMessage());
                }
            }
        } else {
            $this->logger->debug("[Line " . __LINE__ . "] No changes needed for order items in order ID: {$magentoOrderId}");
        }
    }

    /**
     * Edit Order Totals based on SITE data
     *
     * @param int $magentoOrderId
     * @param mixed $OrderStatusData
     * @return void
     */
    public function editOrderTotals(int $magentoOrderId, mixed $OrderStatusData): void
    {
        $this->logger->debug("Starting editOrderTotals for Magento Order ID: {$magentoOrderId}");
        $this->logger->debug("Order Status Data: " . json_encode($OrderStatusData, JSON_PRETTY_PRINT));

        try {
            // Check if OrderStatusData exists in the structure
            if (!isset($OrderStatusData['OrderStatusData'])) {
                $this->logger->debug("OrderStatusData is the main object itself, not a nested key");
                // In this case, the OrderStatusData is the main object
                $orderStatusDataObj = $OrderStatusData;
            } else {
                $this->logger->debug("OrderStatusData is a nested key");
                $orderStatusDataObj = $OrderStatusData['OrderStatusData'];
            }

            // Validate required fields exist
            if (!isset($orderStatusDataObj['AmountGross']) || !isset($orderStatusDataObj['AmountNet']) ||
                !isset($orderStatusDataObj['CurrencyISO']) || !isset($orderStatusDataObj['OrderRowsData'])) {
                $this->logger->error("Missing required fields in OrderStatusData: " . json_encode($orderStatusDataObj));
                throw new \InvalidArgumentException("Missing required fields in OrderStatusData");
            }

            // add recalaculate for base prices
            // If response 'CurrencyISO' same as website currency then base = regular
            // Update order regular amounts
            $order = $this->magentoOrderRepository->get($magentoOrderId);
            $this->logger->debug("Retrieved order #{$magentoOrderId}");

            // Get the store's base currency
            $baseCurrency = $order->getBaseCurrencyCode();
            $orderCurrency = $order->getOrderCurrencyCode();
            $this->logger->debug("Base currency: {$baseCurrency}, Order currency: {$orderCurrency}");

            // Check if we need currency conversion
            $responseCurrency = $orderStatusDataObj['CurrencyISO'];
            $needsConversion = ($baseCurrency !== $responseCurrency);
            $this->logger->debug("Response currency: {$responseCurrency}, Needs conversion: " . ($needsConversion ? 'Yes' : 'No'));

            // Set regular amounts
            $amountGross = $orderStatusDataObj['AmountGross'];
            $amountNet = $orderStatusDataObj['AmountNet'];
            $taxAmount = $amountGross - $amountNet;

            $this->logger->debug("Setting regular amounts - Gross: {$amountGross}, Net: {$amountNet}, Tax: {$taxAmount}");

            $order->setGrandTotal($amountGross);
            $order->setTaxAmount($taxAmount);

            // Set base amounts - either equal to regular amounts or converted
            if (!$needsConversion) {
                // Same currency, base amounts equal regular amounts
                $this->logger->debug("Using same values for base amounts (no conversion needed)");

                $order->setBaseGrandTotal($amountGross);
                $order->setBaseTaxAmount($taxAmount);
            } else {
                // Different currency, need to convert
                $exchangeRate = $this->databaseOperations->getCurrencyExchangeRate($responseCurrency, $baseCurrency);
                $this->logger->debug("Converting with exchange rate: {$exchangeRate} ({$responseCurrency} to {$baseCurrency})");

                $baseGrandTotal = $amountGross * $exchangeRate;
                $baseTaxAmount = $taxAmount * $exchangeRate;

                $this->logger->debug("Calculated base amounts - Grand Total: {$baseGrandTotal}, Tax: {$baseTaxAmount}");

                $order->setBaseGrandTotal($baseGrandTotal);
                $order->setBaseTaxAmount($baseTaxAmount);
            }

            $this->processOrderRowsForTotals($order, $orderStatusDataObj, $needsConversion, $exchangeRate ?? 1.0);

            $this->logger->debug("Saving order changes for Magento Order ID: {$magentoOrderId}");
            $this->magentoOrderRepository->save($order);
            $this->logger->debug("Order #{$magentoOrderId} successfully updated with new totals");

        } catch (\Exception $e) {
            $this->logger->error("Error in editOrderTotals for order #{$magentoOrderId}: " . $e->getMessage());
            $this->logger->debug("Exception trace: " . $e->getTraceAsString());
        }
    }

    /**
     * Process order rows for totals calculation
     */
    protected function processOrderRowsForTotals(Order $order, array $orderStatusDataObj, bool $needsConversion, float $exchangeRate): void
    {
        $this->logger->debug("Processing order rows. Count: " . count($orderStatusDataObj['OrderRowsData']));

        foreach ($orderStatusDataObj['OrderRowsData'] as $index => $product) {
            $this->logger->debug("Processing row #{$index}: ProductNo: {$product['ProductNo']}, PriceNet: {$product['PriceNet']}, PriceGross: {$product['PriceGross']}");

            if ($product['ProductNo'] == 'RABATT20') {
                $discountAmount = -1 * $product['PriceNet'];
                $this->logger->debug("Found discount product. Discount amount: {$discountAmount}");

                $order->setDiscountAmount($discountAmount);

                if (!$needsConversion) {
                    $order->setBaseDiscountAmount($discountAmount);
                    $this->logger->debug("Set base discount amount: {$discountAmount} (no conversion)");
                } else {
                    $baseDiscountAmount = $discountAmount * $exchangeRate;
                    $order->setBaseDiscountAmount($baseDiscountAmount);
                    $this->logger->debug("Set base discount amount: {$baseDiscountAmount} (converted)");
                }
            }

            if ($product['ProductNo'] == '8b') {
                $shippingNet = $product['PriceNet'];
                $shippingGross = $product['PriceGross'];
                $shippingTax = $shippingGross - $shippingNet;

                $this->logger->debug("Found shipping product. Net: {$shippingNet}, Gross: {$shippingGross}, Tax: {$shippingTax}");

                $order->setShippingAmount($shippingNet);
                $order->setShippingInclTax($shippingGross);
                $order->setShippingTaxAmount($shippingTax);

                if (!$needsConversion) {
                    $order->setBaseShippingAmount($shippingNet);
                    $order->setBaseShippingInclTax($shippingGross);
                    $order->setBaseShippingTaxAmount($shippingTax);
                    $this->logger->debug("Set base shipping amounts (no conversion) - Base Net: {$shippingNet}, Base Gross: {$shippingGross}, Base Tax: {$shippingTax}");
                } else {
                    $baseShippingNet = $shippingNet * $exchangeRate;
                    $baseShippingGross = $shippingGross * $exchangeRate;
                    $baseShippingTax = $shippingTax * $exchangeRate;

                    $order->setBaseShippingAmount($baseShippingNet);
                    $order->setBaseShippingInclTax($baseShippingGross);
                    $order->setBaseShippingTaxAmount($baseShippingTax);
                    $this->logger->debug("Set base shipping amounts (converted) - Base Net: {$baseShippingNet}, Base Gross: {$baseShippingGross}, Base Tax: {$baseShippingTax}");
                }
            }
        }
    }

    /**
     * Get quantity from SITE data
     */
    protected function getQtyFromSiteData(string $itemSku, mixed $siteOrderData, int $itemQty): int
    {
        foreach ($siteOrderData['OrderRowsData'] as $item) {
            if ($item['ProductNo'] == $itemSku) {
                return $item['Quantity'];
            }
        }
        return $itemQty;
    }

    /**
     * Get price from Magento data
     */
    protected function getPriceFromMagentoData(OrderItemInterface $item): ?float
    {
        if ($item->getParentItem()) {
            return $item->getParentItem()->getPrice();
        }
        return $item->getPrice();
    }

    /**
     * Get price net from SITE data
     */
    protected function getPriceNetFromSiteData(string $itemSku, mixed $siteOrderData): ?float
    {
        foreach ($siteOrderData['OrderRowsData'] as $item) {
            if ($item['ProductNo'] == $itemSku) {
                return $item['PriceNet'];
            }
        }
        return null;
    }

    /**
     * Get price gross from SITE data
     */
    protected function getPriceGrossFromSiteData(string $itemSku, mixed $siteOrderData): ?float
    {
        foreach ($siteOrderData['OrderRowsData'] as $item) {
            if ($item['ProductNo'] == $itemSku) {
                return $item['PriceGross'];
            }
        }
        return null;
    }

    /**
     * Get discount percentage from SITE data
     */
    protected function getDiscountPercentageFromSiteData(string $itemSku, mixed $siteOrderData): ?float
    {
        foreach ($siteOrderData['OrderRowsData'] as $item) {
            if ($item['ProductNo'] == $itemSku) {
                return $item['DiscountPercentage'];
            }
        }
        return null;
    }

    /**
     * Remove order items
     */
    protected function removeOrderItems(Order $order, array $newOrderData, array $productsToBeRemoved): array
    {
        foreach ($order->getItems() as $item) {
            $itemSku = $item->getProduct()->getSku();
            if (in_array($itemSku, $productsToBeRemoved)) {
                $newOrderData['remove_item'][$item->getItemId()] = $item->getQtyOrdered();
                $this->logger->debug("[Line " . __LINE__ . "] Removing item: {$itemSku}, Item ID: {$item->getItemId()}, Qty: {$item->getQtyOrdered()}");
            }
        }
        return $newOrderData;
    }

    /**
     * Add order items
     */
    protected function addOrderItems(Order $order, array $newOrderData, array $productsToBeAdded, $siteOrderData): array
    {
        $this->logger->debug("[Line " . __LINE__ . "] addOrderItems() - Started processing with order ID: " . $order->getIncrementId());
        $this->logger->debug("[Line " . __LINE__ . "] Products to be added: " . json_encode($productsToBeAdded));

        if (!empty($productsToBeAdded)) {
            $this->logger->debug("[Line " . __LINE__ . "] Processing " . count($productsToBeAdded) . " products to be added...");

            foreach ($productsToBeAdded as $index => $productSku) {
                $this->logger->debug("[Line " . __LINE__ . "] Processing product #{$index}: {$productSku}");

                try {
                    $this->logger->debug("[Line " . __LINE__ . "] Fetching product from repository by SKU: {$productSku}");
                    $product = $this->productRepository->get($productSku);
                    $productId = $simpleProductId = $product->getId();
                    $productSku = $product->getSku();

                    $this->logger->debug("[Line " . __LINE__ . "] Retrieved product - SKU: {$productSku}, ID: {$productId}, Type: {$product->getTypeId()}, Name: {$product->getName()}");

                    // We need configurable product id
                    $this->logger->debug("[Line " . __LINE__ . "] Checking for parent configurable products for ID: {$productId}");
                    $parentIds = $this->configurableType->getParentIdsByChild($productId);

                    if (!empty($parentIds)) {
                        $this->logger->debug("[Line " . __LINE__ . "] Found " . count($parentIds) . " parent products: " . json_encode($parentIds));

                        foreach ($parentIds as $parentIndex => $parentId) {
                            $this->logger->debug("[Line " . __LINE__ . "] Processing parent #{$parentIndex} with ID: {$parentId}");
                            $parentProduct = $this->productRepository->getById($parentId);
                            $productId = $parentProduct->getId();
                            $this->logger->debug("[Line " . __LINE__ . "] Using parent product - ID: {$productId}, SKU: {$parentProduct->getSku()}, Name: {$parentProduct->getName()}");
                        }
                    } else {
                        $this->logger->debug("[Line " . __LINE__ . "] No parent products found, using simple product ID: {$productId}");
                    }

                    $this->logger->debug("[Line " . __LINE__ . "] Getting quantity from site data for SKU: {$productSku}");
                    $siteQty = (int)$this->getQtyFromSiteData($productSku, $siteOrderData, 1);
                    $this->logger->debug("[Line " . __LINE__ . "] Retrieved quantity: {$siteQty}");

                    $this->logger->debug("[Line " . __LINE__ . "] Getting price from site data for SKU: {$productSku}");
                    $priceNet = (float)$this->getPriceNetFromSiteData($productSku, $siteOrderData);
                    $this->logger->debug("[Line " . __LINE__ . "] Retrieved price: {$priceNet}");

                    $this->logger->debug("[Line " . __LINE__ . "] Getting discount percentage from site data for SKU: {$productSku}");
                    $discountPercentage = (float)$this->getDiscountPercentageFromSiteData($productSku, $siteOrderData);
                    $this->logger->debug("[Line " . __LINE__ . "] Retrieved discount percentage: {$discountPercentage}");

                    $this->logger->debug("[Line " . __LINE__ . "] Setting up new order data - adding simple product ID: {$simpleProductId}");
                    $newOrderData['add_item'] = [$simpleProductId => $simpleProductId];

                    $this->logger->debug("[Line " . __LINE__ . "] Setting price-excl-tax for product ID: {$productId} to {$priceNet}");
                    $newOrderData['price-excl-tax']['preview-' . $productId] = $priceNet;

                    $this->logger->debug("[Line " . __LINE__ . "] Setting ordered quantity for product ID: {$productId} to {$siteQty}");
                    $newOrderData['col-ordered-qty']['preview-' . $productId] = $siteQty;

                    $this->logger->debug("[Line " . __LINE__ . "] Setting discount percentage for product ID: {$productId} to {$discountPercentage}");
                    $newOrderData['col-discont']['preview-' . $productId] = $discountPercentage;

                    $this->logger->debug("[Line " . __LINE__ . "] Successfully added new product: {$productSku}, Product ID: {$productId}, Simple ID: {$simpleProductId}, Qty: {$siteQty}, Price: {$priceNet}, Discount: {$discountPercentage}%");
                } catch (\Exception $e) {
                    $this->logger->error("[Line " . __LINE__ . "] Error adding product {$productSku}: " . $e->getMessage());
                    $this->logger->error("[Line " . __LINE__ . "] Exception trace: " . $e->getTraceAsString());
                }
            }

            $this->logger->debug("[Line " . __LINE__ . "] Finished processing all products");
        } else {
            $this->logger->debug("[Line " . __LINE__ . "] No products to be added, skipping product processing");
        }

        $this->logger->debug("[Line " . __LINE__ . "] addOrderItems() - Completed, returning updated order data");

        return $newOrderData;
    }

    /**
     * Modify order items
     */
    protected function modifyOrderItems(array $newOrderData, array $productsToBeModified): array
    {
        // Modify Item
        foreach ($productsToBeModified as $ptbmItemId => $productData) {
            $newOrderData['price-excl-tax'][$ptbmItemId] = $productData['price-excl-tax'];
            $newOrderData['col-ordered-qty'][$ptbmItemId] = $productData['new_qty'];
            $newOrderData['col-discont'][$ptbmItemId] = $productData['col-discont'];

            $this->logger->debug("[Line " . __LINE__ . "] Update order item: {$ptbmItemId}, Product Data:"
                . json_encode($productData)
                . "New Product Data:" . json_encode($newOrderData));
        }
        $this->logger->debug("[Line " . __LINE__ . "] Modified Items array: " . var_export($newOrderData, true));
        return $newOrderData;
    }

    /**
     * Get products to be modified
     */
    protected function getProductsToBeModified(Order $order, array $productsToBeRemoved, array $siteOrderData): array
    {
        // Initialize the array for products that need quantity modification
        $productsToBeModified = [];

        // Check for quantity differences between Magento order items and site order data
        foreach ($order->getItems() as $item) {
            if ($item->getProductType() == 'configurable') {
                continue;
            }
            $itemSku = $item->getProduct()->getSku();
            // Skip items that are already being removed
            if (in_array($itemSku, $productsToBeRemoved)) {
                continue;
            }

            $itemId = $item->getItemId();
            if (!empty($item->getParentItemId())) {
                $itemId = $item->getParentItemId();
            }
            $productId = $item->getProductId();
            $currentQty = (int)$item->getQtyOrdered();
            $currentPrice = (float)$this->getPriceFromMagentoData($item);
            $currentDiscountPercentage = (float)$item->getDiscountPercent();
            $siteQty = (int)$this->getQtyFromSiteData($itemSku, $siteOrderData, $currentQty);
            $priceNet = (float)$this->getPriceNetFromSiteData($itemSku, $siteOrderData);
            $priceGross = (float)$this->getPriceGrossFromSiteData($itemSku, $siteOrderData);
            $discountPercentage = (float)$this->getDiscountPercentageFromSiteData($itemSku, $siteOrderData);

            // If the quantity/price/discount is different, add to the array of products to be modified
            if (
                ($currentQty !== $siteQty) ||
                ($currentPrice !== $priceNet) ||
                ($currentDiscountPercentage !== $discountPercentage)) {
                $productsToBeModified[$itemId] = [
                    'product_id' => $productId,
                    'sku' => $itemSku,
                    'current_qty' => $currentQty,
                    'new_qty' => $siteQty,
                    'price-excl-tax' => $priceNet,
                    'price-incl-tax' => $priceGross,
                    'current_price' => $currentPrice,
                    'col-discont' => $discountPercentage,
                    'current-discount-percent' => $currentDiscountPercentage,
                ];
                $this->logger->debug("[Line " . __LINE__ . "] Changes for item SKU: {$itemSku}, Product ID: {$productId}, Item ID: {$itemId}, Old Qty: {$currentQty}, New Qty: {$siteQty}, Old Price: {$currentPrice}, New Price(PriceNet): {$priceNet}, (PriceGross){$priceGross} Old discount: {$currentDiscountPercentage}, New discount(DiscountPercentage): {$discountPercentage}");
            }
        }
        $this->logger->debug("[Line " . __LINE__ . "] List of products to be modified: " . var_export($productsToBeModified, true));

        return $productsToBeModified;
    }

    /**
     * Get SKU list for fix functions
     */
    protected function getSkuForFixFunctions(array $productsToBeAdded, array $productsToBeModified): array
    {
        $skuForFixFunctions = [];
        if(!empty($productsToBeAdded)) {
            foreach ($productsToBeAdded as $sku) {
                $skuForFixFunctions[] = $sku;
            }
        }
        if(!empty($productsToBeModified)) {
            foreach ($productsToBeModified as $item) {
                $skuForFixFunctions[] = $item['sku'];
            }
        }
        return $skuForFixFunctions;
    }

    /**
     * Fix discount for existing products by updating discount_percent, discount_amount,
     * and base_discount_amount in sales_order_item table via direct SQL
     */
    protected function fixDiscountForExistProducts(Order $order, array $siteOrderData, array $skuForFixFunctions): void
    {
        $this->logger->info(
            sprintf(
                "Starting discount fix for order #%s with %d items",
                $order->getId(),
                count($order->getAllItems())
            )
        );

        try {
            $connection = $this->resourceConnection->getConnection();
            $orderItemTable = $this->resourceConnection->getTableName('sales_order_item');

            // Begin transaction
            $connection->beginTransaction();
            $this->logger->debug("Transaction started for order #{$order->getId()}");

            // Get all order items (including configurable products)
            $orderItems = $order->getAllItems();
            $this->logger->debug(
                sprintf(
                    "Processing %d order items for order #%s",
                    count($orderItems),
                    $order->getId()
                )
            );

            $itemsProcessed = 0;
            $itemsUpdated = 0;

            foreach ($orderItems as $item) {
                $sku = $item->getSku();
                if (!in_array($sku, $skuForFixFunctions)) {
                    continue;
                }
                $itemId = $item->getItemId();
                $itemsProcessed++;

                $this->logger->debug(
                    sprintf(
                        "Processing item %d/%d: ID %s, SKU %s, Type %s",
                        $itemsProcessed,
                        count($orderItems),
                        $itemId,
                        $sku,
                        $item->getProductType()
                    )
                );

                // Find matching item in siteOrderData
                $matchFound = false;
                foreach ($siteOrderData['OrderRowsData'] as $index => $siteItem) {
                    if ($siteItem['ProductNo'] === $sku) {
                        $matchFound = true;
                        $this->logger->debug(
                            sprintf(
                                "Match found for SKU %s in siteOrderData at index %d",
                                $sku,
                                $index
                            )
                        );

                        // Get discount percentage from site data
                        $discountPercent = (float)($siteItem['DiscountPercentage'] ?? 0);

                        // Get PriceNet from site data, fallback to Magento price
                        $priceNet = (float)($siteItem['PriceNet'] ?? $item->getPrice());
                        $this->logger->debug(
                            sprintf(
                                "Item %s: Using price %s (site data: %s, Magento price: %s)",
                                $itemId,
                                $priceNet,
                                $siteItem['PriceNet'] ?? 'N/A',
                                $item->getPrice()
                            )
                        );

                        // Calculate discount amount
                        $qtyOrdered = $item->getQtyOrdered();
                        $discountAmount = $priceNet * ($discountPercent / 100) * $qtyOrdered;
                        $baseDiscountAmount = $discountAmount; // Assuming base currency same as order currency

                        $this->logger->debug(
                            sprintf(
                                "Item %s: Initial calculation - Price: %s, Discount%%: %s, Qty: %s, Discount Amount: %s",
                                $itemId,
                                $priceNet,
                                $discountPercent,
                                $qtyOrdered,
                                $discountAmount
                            )
                        );

                        // If currencies differ, convert base amount
                        $orderCurrency = $order->getOrderCurrencyCode();
                        $baseCurrency = $order->getBaseCurrencyCode();

                        if ($orderCurrency !== $baseCurrency) {
                            $this->logger->debug(
                                sprintf(
                                    "Currency conversion needed: %s to %s for item %s",
                                    $orderCurrency,
                                    $baseCurrency,
                                    $itemId
                                )
                            );

                            $exchangeRate = $this->databaseOperations->getCurrencyExchangeRate($orderCurrency, $baseCurrency);
                            $baseDiscountAmount = $discountAmount * $exchangeRate;

                            $this->logger->debug(
                                sprintf(
                                    "Exchange rate: %s, Converted base discount amount: %s",
                                    $exchangeRate,
                                    $baseDiscountAmount
                                )
                            );
                        }

                        // Prepare update data
                        $updateData = [
                            'discount_percent' => $discountPercent,
                            'discount_amount' => $discountAmount,
                            'base_discount_amount' => $baseDiscountAmount,
                            'updated_at' => date('Y-m-d H:i:s')
                        ];

                        // Update sales_order_item table
                        $affectedRows = $connection->update(
                            $orderItemTable,
                            $updateData,
                            ['item_id = ?' => $itemId]
                        );

                        if ($affectedRows) {
                            $itemsUpdated++;
                            $this->logger->info(
                                sprintf(
                                    "Updated discount for item %s (SKU: %s, Type: %s) - Percent: %s, Amount: %s, Base Amount: %s",
                                    $itemId,
                                    $sku,
                                    $item->getProductType(),
                                    $discountPercent,
                                    $discountAmount,
                                    $baseDiscountAmount
                                )
                            );
                        } else {
                            $this->logger->warning(
                                sprintf(
                                    "Update query returned 0 affected rows for item %s (SKU: %s)",
                                    $itemId,
                                    $sku
                                )
                            );
                        }

                        break; // Found matching item, move to next order item
                    }
                }

                if (!$matchFound) {
                    $this->logger->warning(
                        sprintf(
                            "No matching SKU found in siteOrderData for item %s (SKU: %s, Type: %s)",
                            $itemId,
                            $sku,
                            $item->getProductType()
                        )
                    );
                }
            }

            // Commit transaction
            $connection->commit();
            $this->logger->info(
                sprintf(
                    "Successfully updated discounts for order #%s: %d/%d items updated",
                    $order->getId(),
                    $itemsUpdated,
                    $itemsProcessed
                )
            );
        } catch (\Exception $e) {
            // Rollback transaction on error
            if (isset($connection)) {
                $connection->rollBack();
                $this->logger->debug("Transaction rolled back for order #{$order->getId()}");
            }

            $this->logger->error(
                sprintf(
                    "Failed to update discounts for order #%s: %s\nStack trace: %s",
                    $order->getId(),
                    $e->getMessage(),
                    $e->getTraceAsString()
                )
            );
        }
    }

    /**
     * Set original_price and base_original_price in sales_order_item table via direct SQL,
     * mimicking Magento's behavior on order creation by using catalog prices
     */
    protected function setOriginalPricesForOrderItems(Order $order, array $skuForFixFunctions): void
    {
        $this->logger->debug("Starting setOriginalPricesForOrderItems for Order ID: {$order->getId()}");

        try {
            // Get database connection
            $connection = $this->resourceConnection->getConnection();
            $orderItemTable = $this->resourceConnection->getTableName('sales_order_item');

            // Begin transaction
            $connection->beginTransaction();
            $this->logger->debug("Transaction started for order #{$order->getId()}");

            // Get all order items
            $orderItems = $order->getAllItems();
            $this->logger->debug(
                sprintf(
                    "Processing %d order items for order #%s",
                    count($orderItems),
                    $order->getId()
                )
            );

            $itemsUpdated = 0;

            foreach ($orderItems as $item) {
                $itemId = $item->getItemId();
                $sku = $item->getSku();
                if (!in_array($sku, $skuForFixFunctions)) {
                    continue;
                }
                try {
                    // Get the product from repository
                    $product = $this->productRepository->get($sku);

                    // Get product price from catalog
                    $catalogPrice = $product->getPrice();

                    // Determine if we need currency conversion
                    $orderCurrency = $order->getOrderCurrencyCode();
                    $baseCurrency = $order->getBaseCurrencyCode();
                    $needsConversion = ($orderCurrency !== $baseCurrency);

                    // Set original price
                    $originalPrice = $catalogPrice;
                    $baseOriginalPrice = $catalogPrice;

                    // If currencies differ, convert base amount
                    if ($needsConversion) {
                        $this->logger->debug(
                            sprintf(
                                "Currency conversion needed: %s to %s for item %s",
                                $orderCurrency,
                                $baseCurrency,
                                $itemId
                            )
                        );

                        $exchangeRate = $this->databaseOperations->getCurrencyExchangeRate($orderCurrency, $baseCurrency);
                        $baseOriginalPrice = $originalPrice * $exchangeRate;

                        $this->logger->debug(
                            sprintf(
                                "Exchange rate: %s, Converted base original price: %s",
                                $exchangeRate,
                                $baseOriginalPrice
                            )
                        );
                    }

                    // Prepare update data
                    $updateData = [
                        'original_price' => $originalPrice,
                        'base_original_price' => $baseOriginalPrice,
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    // Log values before update
                    $this->logger->debug(
                        sprintf(
                            "Item %s (SKU: %s): Setting original_price: %s, base_original_price: %s",
                            $itemId,
                            $sku,
                            $originalPrice,
                            $baseOriginalPrice
                        )
                    );

                    // Update sales_order_item table
                    $affectedRows = $connection->update(
                        $orderItemTable,
                        $updateData,
                        ['item_id = ?' => $itemId]
                    );

                    if ($affectedRows) {
                        $itemsUpdated++;
                        $this->logger->debug(
                            sprintf(
                                "Updated original prices for item %s (SKU: %s)",
                                $itemId,
                                $sku
                            )
                        );
                    } else {
                        $this->logger->warning(
                            sprintf(
                                "Update query returned 0 affected rows for item %s (SKU: %s)",
                                $itemId,
                                $sku
                            )
                        );
                    }

                } catch (\Exception $e) {
                    $this->logger->warning(
                        sprintf(
                            "Failed to update original prices for item %s (SKU: %s): %s",
                            $itemId,
                            $sku,
                            $e->getMessage()
                        )
                    );
                    // Continue with other items even if one fails
                    continue;
                }
            }

            // Commit transaction
            $connection->commit();
            $this->logger->info(
                sprintf(
                    "Successfully updated original prices for order #%s: %d items updated",
                    $order->getId(),
                    $itemsUpdated
                )
            );

        } catch (\Exception $e) {
            // Rollback transaction on error
            if (isset($connection)) {
                $connection->rollBack();
                $this->logger->debug("Transaction rolled back for order #{$order->getId()}");
            }

            $this->logger->error(
                sprintf(
                    "Failed to update original prices for order #%s: %s\nStack trace: %s",
                    $order->getId(),
                    $e->getMessage(),
                    $e->getTraceAsString()
                )
            );
        }
    }
}
