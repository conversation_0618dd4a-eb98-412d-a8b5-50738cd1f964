<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationCore\Model\Api;

use MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi\OrderExporter;
use MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi\OrderSynchronizer;
use MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi\PaymentProcessor;
use Magento\Framework\Exception\CouldNotSaveException;

/**
 * Main SiteOrderApi Facade Class
 *
 * This class serves as the main entry point for SITE/ERP order integration operations.
 * It replaces the original monolithic SiteOrderApi class (3431 lines) by delegating
 * responsibilities to specialized classes following the Single Responsibility Principle.
 *
 * Key Responsibilities:
 * - Acts as a facade for order export, sync, and payment operations
 * - Maintains backward compatibility with existing code
 * - Delegates complex operations to specialized handler classes
 *
 * Usage:
 * - Export orders to SITE/ERP: exportSiteOrders()
 * - Sync order status from SITE/ERP: syncSiteOrders()
 * - Export payment transactions: exportSiteOrderPayments()
 *
 * This refactoring improves maintainability, testability, and follows Magento 2 best practices.
 *
 * @since Refactored from MadHat\SiteIntegrationOrder\Model\Api\SiteOrderApi
 */
class SiteOrderApi
{
    /**
     * @var OrderExporter
     */
    protected OrderExporter $orderExporter;

    /**
     * @var OrderSynchronizer
     */
    protected OrderSynchronizer $orderSynchronizer;

    /**
     * @var PaymentProcessor
     */
    protected PaymentProcessor $paymentProcessor;

    public function __construct(
        OrderExporter $orderExporter,
        OrderSynchronizer $orderSynchronizer,
        PaymentProcessor $paymentProcessor
    ) {
        $this->orderExporter = $orderExporter;
        $this->orderSynchronizer = $orderSynchronizer;
        $this->paymentProcessor = $paymentProcessor;
    }

    /**
     * Export Magento Orders to SITE/ERP
     *
     * @param array $orderIncrementIds
     * @return array
     * @throws CouldNotSaveException
     */
    public function exportSiteOrders(array $orderIncrementIds = []): array
    {
        return $this->orderExporter->exportSiteOrders($orderIncrementIds);
    }

    /**
     * Sync Magento Orders as "Picked|Invoiced" as per SITE/ERP status
     *
     * @param array $orderIncrementIds
     * @return array
     */
    public function syncSiteOrders(array $orderIncrementIds = []): array
    {
        return $this->orderSynchronizer->syncSiteOrders($orderIncrementIds);
    }

    /**
     * Export order payment transaction details to SITE
     *
     * @param array $incrementIds
     * @return array
     */
    public function exportSiteOrderPayments(array $incrementIds = []): array
    {
        return $this->paymentProcessor->exportSiteOrderPayments($incrementIds);
    }
}
