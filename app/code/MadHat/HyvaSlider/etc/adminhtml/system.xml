<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="hyva_slider" translate="label" sortOrder="130" showInDefault="1" showInWebsite="1" showInStore="0">
            <class>separator-top</class>
            <label>Hyva Slider Config</label>
            <tab>madhat</tab>
            <resource>MadHat_DbLogger::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>General Configuration</label>
                <field id="home_page_main_slider_image_json" translate="label" type="textarea" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Home page main slider_image_json</label>
                    <comment>Store JSON array image config string for home page main slider</comment>
                </field>
                <field id="home_page_banner_slider_image_json" translate="label" type="textarea" sortOrder="20" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Home page banner slider image json</label>
                    <comment>Store JSON array image config string for home page banner slider</comment>
                </field>
            </group>
        </section>
    </system>
</config>
