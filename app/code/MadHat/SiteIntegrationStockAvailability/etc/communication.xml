<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Communication/etc/communication.xsd">
    <topic name="site.techoutlet.stockavailability" request="MadHat\SiteIntegrationStockAvailability\Api\Data\StockAvailabilityDataInterface[]">
        <handler name="madhat.siteintegrationstockavailability.consumer"
                 type="MadHat\SiteIntegrationStockAvailability\Model\RabbitMQ\StockAvailabilityConsumer" method="processMessage"/>
    </topic>

<!-- For developer only -->
<!--    <topic name="site.techoutlet-dev.stockavailability.nitinh" request="MadHat\SiteIntegrationStockAvailability\Api\Data\StockAvailabilityDataInterface[]">-->
<!--        <handler name="madhat.siteintegrationstockavailability.consumer"-->
<!--                 type="MadHat\SiteIntegrationStockAvailability\Model\RabbitMQ\StockAvailabilityConsumer" method="processMessage"/>-->
<!--    </topic>-->
</config>
