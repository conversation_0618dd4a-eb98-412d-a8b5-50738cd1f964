<?php


namespace MadHat\DbLogger\Block\Adminhtml;

use MadHat\DbLogger\Model\DbLoggerFactory;
use MadHat\DbLogger\Model\DbLogger;
use Magento\Framework\Json\Helper\Data as JsonHelper;
use Magento\Directory\Helper\Data as DirectoryHelper;

class DbLoggerDetails extends \Magento\Backend\Block\Template
{
    /**
     * @var DbLoggerFactory
     */
    protected $dbLoggerFactory;

    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        DbLoggerFactory $dbLoggerFactory,
        array $data = [],
        ?JsonHelper $jsonHelper = null,
        ?DirectoryHelper $directoryHelper = null
    ) {
        parent::__construct($context, $data, $jsonHelper, $directoryHelper);
        $this->dbLoggerFactory = $dbLoggerFactory;
    }

    public function getDbLoggerDetails(): DbLogger
    {
        $entityId = $this->getRequest()->getParam('entity_id');
        return $this->dbLoggerFactory->create()->load($entityId);
    }
}
