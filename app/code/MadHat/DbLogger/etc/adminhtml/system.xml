<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="mageplaza" translate="label" sortOrder="10">
            <label>Mageplaza</label>
        </tab>
        <section id="dblogger" translate="label" sortOrder="130" showInDefault="1" showInWebsite="1" showInStore="0">
            <class>separator-top</class>
            <label>DB Logger</label>
            <tab>madhat</tab>
            <resource>MadHat_DbLogger::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="0">
                <label>General Configuration</label>
                <field id="enable" translate="label" type="select" sortOrder="1" showInDefault="1" showInWebsite="1"
                       showInStore="0">
                    <label>Module Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="log_identifier" translate="label" type="multiselect" sortOrder="20" showInDefault="1"
                       showInWebsite="1" showInStore="0" canRestore="1">
                    <label>Log Identifier</label>
                    <source_model>MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider</source_model>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="clean_log_before_days" translate="label" type="text" sortOrder="20" showInDefault="1"
                       showInWebsite="0" showInStore="0">
                    <label>Clean Log Before Days</label>
                    <source_model>MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider</source_model>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
