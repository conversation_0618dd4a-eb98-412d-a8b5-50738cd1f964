<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="site" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0" translate="label">
            <label>SITE Product Import</label>
            <tab>madhat</tab>
            <resource>MadHat_SiteIntegrationProducts::site_integration_products_config</resource>
            <group id="pdp" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                <label>Product detail Config</label>
                <field id="product_attribute_set_mapping" type="textarea" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0" translate="label comment">
                    <label>Product Attribute Set Mapping</label>
                    <comment>Attribute set and variant mapping JSON string used on PDP page.</comment>
                </field>
            </group>
        </section>
    </system>
</config>

