<?php

namespace MadHat\Catalog\Block\Adminhtml\Edit;

/**
 * Adminhtml Review Edit Form
 */
class Form extends \MageWorx\XReviewBase\Block\Adminhtml\Edit\Form
{
    /**
     * Prepare edit review form
     *
     * @return $this
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    protected function _prepareForm()
    {
        parent::_prepareForm();

        $form = $this->getForm();
        $form->setDataObject($this->getReview());

        $originalFieldset = $form->getElement('review_details');

        $originalFieldset->addField(
            'variant_info',
            'text',
            [
                'label' => __("Madhat Variant Info"),
                'name'  => 'variant_info'
            ],
            'answer'
        );
        $originalFieldset->addField(
            'variant_sku',
            'text',
            [
                'label' => __("Madhat Variant Sku"),
                'name'  => 'variant_sku'
            ]
        );

        $form->setValues($this->getReview()->getData());

        return $this;
    }
}
