<?php
declare(strict_types=1);

namespace MadHat\OrderIntegration\Api;

use MadHat\OrderIntegration\Api\Data\MadhatCustomerInfoInterface;
use MadHat\OrderIntegration\Api\Data\MadhatCustomerInfoSearchResultsInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

interface MadhatCustomerInfoRepositoryInterface
{

    /**
     * Save madhat_customer_info
     * @param MadhatCustomerInfoInterface $madhatCustomerInfo
     * @return MadhatCustomerInfoInterface
     * @throws LocalizedException
     */
    public function save(
        MadhatCustomerInfoInterface $madhatCustomerInfo
    ): MadhatCustomerInfoInterface;

    /**
     * Retrieve madhat_customer_info
     * @param string $entityId
     * @return MadhatCustomerInfoInterface
     * @throws LocalizedException
     */
    public function get(string $entityId): MadhatCustomerInfoInterface;

    /**
     * Retrieve madhat_customer_info
     *
     * @param string $customerEmail
     * @return MadhatCustomerInfoInterface
     * @throws LocalizedException
     */
    public function getByEmail(string $customerEmail): MadhatCustomerInfoInterface;

    /**
     * Retrieve madhat_customer_info matching the specified criteria.
     * @param SearchCriteriaInterface $searchCriteria
     * @return MadhatCustomerInfoSearchResultsInterface
     * @throws LocalizedException
     */
    public function getList(
        SearchCriteriaInterface $searchCriteria
    ): MadhatCustomerInfoSearchResultsInterface;

    /**
     * Delete madhat_customer_info
     * @param MadhatCustomerInfoInterface $madhatCustomerInfo
     * @return bool true on success
     * @throws LocalizedException
     */
    public function delete(
        MadhatCustomerInfoInterface $madhatCustomerInfo
    ): bool;

    /**
     * Delete madhat_customer_info by ID
     * @param string $entityId
     * @return bool true on success
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById(string $entityId): bool;
}

