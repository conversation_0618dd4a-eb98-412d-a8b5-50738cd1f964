<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="MadHat\OrderIntegration\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="MadHat\OrderIntegration\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">MadHat_OrdersLogHandler</argument>
            <argument name="handlers"  xsi:type="array">
                <item name="system" xsi:type="object">MadHat\OrderIntegration\Logger\Handler</item>
            </argument>
        </arguments>
    </type>

    <preference for="MadHat\OrderIntegration\Api\MadhatOrderInfoRepositoryInterface" type="MadHat\OrderIntegration\Model\MadhatOrderInfoRepository"/>
    <preference for="MadHat\OrderIntegration\Api\Data\MadhatOrderInfoInterface" type="MadHat\OrderIntegration\Model\MadhatOrderInfo"/>
    <preference for="MadHat\OrderIntegration\Api\Data\MadhatOrderInfoSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>

    <preference for="MadHat\OrderIntegration\Api\MadhatOrderItemInfoRepositoryInterface" type="MadHat\OrderIntegration\Model\MadhatOrderItemInfoRepository"/>
    <preference for="MadHat\OrderIntegration\Api\Data\MadhatOrderItemInfoInterface" type="MadHat\OrderIntegration\Model\MadhatOrderItemInfo"/>
    <preference for="MadHat\OrderIntegration\Api\Data\MadhatOrderItemInfoSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>

    <preference for="MadHat\OrderIntegration\Api\MadhatCustomerInfoRepositoryInterface" type="MadHat\OrderIntegration\Model\MadhatCustomerInfoRepository"/>
    <preference for="MadHat\OrderIntegration\Api\Data\MadhatCustomerInfoInterface" type="MadHat\OrderIntegration\Model\MadhatCustomerInfo"/>
    <preference for="MadHat\OrderIntegration\Api\Data\MadhatCustomerInfoSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>

    <type name="Magento\Sales\Api\OrderRepositoryInterface">
        <plugin name="madhat.order.extension.attributes.info"
                type="MadHat\OrderIntegration\Plugin\MadHatOrderInfo"/>
    </type>

    <type name="MadHat\OrderIntegration\Model\OrderGridRefresher">
        <arguments>
            <argument name="entityGrid" xsi:type="object">Magento\Sales\Model\ResourceModel\Order\Grid</argument>
        </arguments>
    </type>

    <virtualType name="Magento\Sales\Model\ResourceModel\Order\Grid" type="Magento\Sales\Model\ResourceModel\Grid">
        <arguments>
            <argument name="joins" xsi:type="array">
                <item name="madhat_order_info" xsi:type="array">
                    <item name="table" xsi:type="string">madhat_order_info</item>
                    <item name="origin_column" xsi:type="string">entity_id</item>
                    <item name="target_column" xsi:type="string">order_id</item>
                </item>
            </argument>
            <argument name="columns" xsi:type="array">
                <item name="fc_order_id" xsi:type="string">madhat_order_info.fc_order_id</item>
                <item name="site_order_id" xsi:type="string">madhat_order_info.site_order_id</item>
                <item name="fc_order_ref_id" xsi:type="string">madhat_order_info.fc_order_ref_id</item>
                <item name="site_is_picked" xsi:type="string">madhat_order_info.site_is_picked</item>
                <item name="site_is_invoiced" xsi:type="string">madhat_order_info.site_is_invoiced</item>
            </argument>
        </arguments>
    </virtualType>
</config>
