<?php
/** @var $block \MadHat\OrderIntegration\Block\Adminhtml\Order\View\Tab\MadhatOrderInfo */
$order = $block->getOrder();
$orderId = $order ? $order->getId() : null;

// Fetch madhat_order_info record for this order
$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$repo = $objectManager->get('MadHat\\OrderIntegration\\Model\\MadhatOrderInfoRepository');
try {
    $info = $repo->getByOrderId($orderId);
} catch (Exception $e) {
    $info = null;
}

$groups = [
    'FulfilmentCrowd' => [
        'fc_order_id' => 'FC Order Id',
        'fc_order_ref_id' => 'FC Order Ref Id',
        'site_is_fc_order_id_sync' => 'Is FC Order Id Sync on SITE',
        'fulfilment_workflow' => 'Fulfilment Workflow Type',
    ],
    'SITE' => [
        'site_order_id' => 'SITE Order Id',
        'site_is_captured' => 'Is Captured for SITE',
        'is_picked' => 'Is Picked',
        'site_is_picked' => 'Is Picked on SITE',
        'is_invoiced' => 'Is Invoiced',
        'site_is_invoiced' => 'Is Invoiced on SITE',
        'site_invoice_no' => 'Invoice No',
        'site_invoice_file' => 'Invoice File',
        'site_invoice_url' => 'Invoice URL',
        'site_order_export_attempts' => 'SITE Order Export Attempts',
        'site_order_export_fail_notify' => 'SITE Order Export Fail Notify',
    ],
    'OTHER' => [
        'site_round' => 'SITE ROUND Product',
    ],
];

$flagFields = [
    'is_picked',
    'is_invoiced',
    'site_is_picked',
    'site_is_invoiced',
    'site_is_fc_order_id_sync',
    'site_is_captured',
];
?>
<div id="madhat-order-info-form">
    <form id="madhat-order-info-edit-form">
        <input type="hidden" name="order_id" value="<?= (int)$orderId ?>" />
        <?php foreach ($groups as $groupLabel => $fields): ?>
            <fieldset class="admin__fieldset">
                <legend class="admin__legend"><span><?= __($groupLabel) ?></span></legend>
                <?php foreach ($fields as $field => $label): ?>
                    <div class="admin__field">
                        <label class="admin__field-label" for="<?= $field ?>"><?= __($label) ?></label>
                        <div class="admin__field-control">
                            <?php if (in_array($field, $flagFields)): ?>
                                <select name="<?= $field ?>" id="<?= $field ?>" class="admin__control-select">
                                    <option value=""<?= ($info && ($info->getData($field) === null || $info->getData($field) === '')) ? ' selected' : '' ?>><?= __('NULL') ?></option>
                                    <option value="1"<?= ($info && $info->getData($field) == 1) ? ' selected' : '' ?>>1</option>
                                </select>
                            <?php else: ?>
                                <input type="text" name="<?= $field ?>" id="<?= $field ?>" value="<?= $info ? htmlspecialchars(($info->getData($field) ?? '')) : '' ?>" class="admin__control-text" />
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </fieldset>
        <?php endforeach; ?>
        <button type="button" id="madhat-order-info-save" class="action-default scalable save primary">
            <?= __('Save') ?>
        </button>
    </form>
    <div id="madhat-order-info-result"></div>
</div>
<script type="text/javascript">
require(['jquery'], function($) {
    $('#madhat-order-info-save').on('click', function() {
        var data = $('#madhat-order-info-edit-form').serialize();
        $.ajax({
            url: '<?= $block->getUrl("madhat_orderintegration/orderinfo/save") ?>',
            type: 'POST',
            data: data,
            showLoader: true,
            success: function(response) {
                $('#madhat-order-info-result').html('<div class="message-success">'+response.message+'</div>');
            },
            error: function(xhr) {
                $('#madhat-order-info-result').html('<div class="message-error">'+xhr.responseText+'</div>');
            }
        });
    });
});
</script>
