<?php
namespace MadHat\OrderIntegration\Block\Adminhtml\Order\View\Tab;

use Magento\Backend\Block\Widget\Tab\TabInterface;
use Magento\Backend\Block\Widget\Tab;
use Magento\Framework\Registry;
use Magento\Backend\Block\Template\Context;

class MadhatOrderInfo extends Tab implements TabInterface
{
    protected $_template = 'MadHat_OrderIntegration::order/view/tab/madhat_order_info.phtml';

    protected $coreRegistry;
    protected $_authorization;

    public function __construct(
        Context $context,
        Registry $coreRegistry,
        array $data = []
    ) {
        $this->coreRegistry = $coreRegistry;
        $this->_authorization = $context->getAuthorization();
        parent::__construct($context, $data);
    }

    public function getOrder()
    {
        return $this->coreRegistry->registry('current_order');
    }

    public function getTabLabel()
    {
        return __('Madhat order info');
    }

    public function getTabTitle()
    {
        return __('Madhat order info');
    }

    public function canShowTab()
    {
        // Only show for administrators (Magento_Backend::admin role)
        return $this->_authorization->isAllowed('MadHat_OrderIntegration::orderinfo');
    }

    public function isHidden()
    {
        return false;
    }
}
