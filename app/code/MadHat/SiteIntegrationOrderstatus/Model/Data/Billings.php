<?php

namespace MadHat\SiteIntegrationOrderstatus\Model\Data;

use MadHat\SiteIntegrationOrderstatus\Api\Data\BillingsInterface;
use Magento\Framework\DataObject;

class Billings extends DataObject implements BillingsInterface
{
    public function getInvoiceNo()
    {
        return $this->getData('InvoiceNo');
    }

    public function setInvoiceNo(?string $invoiceNo)
    {
        $this->setData('InvoiceNo', $invoiceNo);
    }

    public function getInvoiceFile()
    {
        return $this->getData('InvoiceFile');
    }

    public function setInvoiceFile(?string $invoiceFile)
    {
        $this->setData('InvoiceFile', $invoiceFile);
    }

    public function getInvoiceUrl()
    {
        return $this->getData('InvoiceUrl');
    }

    public function setInvoiceUrl(?string $invoiceUrl)
    {
        $this->setData('InvoiceUrl', $invoiceUrl);
    }
}
