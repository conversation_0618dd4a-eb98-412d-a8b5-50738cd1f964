<?php

namespace MadHat\SiteIntegrationOrderstatus\Model;

use MadHat\SiteIntegrationOrderstatus\Api\Data\OrderstatusDataInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

class OrderstatusProcessor
{
    /** @var LoggerInterface */
    private LoggerInterface $logger;

    /** @var OrderRepositoryInterface */
    private OrderRepositoryInterface $orderRepository;

    /** @var \Magento\Framework\Api\SearchCriteriaBuilder */
    private $searchCriteriaBuilder;

    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param LoggerInterface $logger
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     */
    public function __construct(
        OrderRepositoryInterface $orderRepository,
        LoggerInterface $logger,
        \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
    ) {
        $this->orderRepository = $orderRepository;
        $this->logger = $logger;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
    }

    /**
     * @param OrderstatusDataInterface $item
     * @return bool
     */
    public function processItem(OrderstatusDataInterface $item): bool
    {
        $webOrderNo = $item->getWebOrderNo();
        if (!$webOrderNo) {
            $this->logger->warning('WebOrderNo is missing in the message.');
            return false;
        }

        try {
            $order = $this->loadOrderByIncrementId($webOrderNo);
            if ($order) {
                $this->logger->info(sprintf(
                    'Processing order status update for WebOrderNo %s. Status: %s (%s)',
                    $webOrderNo,
                    $item->getStatusText(),
                    $item->getStatus()
                ));

                // TODO: Implement order status update logic here
                // This could include:
                // - Updating order status based on the Status/StatusText
                // - Processing shipment information if ShipmentStatus indicates shipped
                // - Handling billing information if Billings data is present
                // - Processing product shipments data

                return true;
            } else {
                $this->logger->warning(sprintf('Order with WebOrderNo %s not found.', $webOrderNo));
                return false;
            }
        } catch (NoSuchEntityException $e) {
            $this->logger->error(sprintf('Order with WebOrderNo %s not found: %s', $webOrderNo, $e->getMessage()));
            return false;
        } catch (LocalizedException $e) {
            $this->logger->error(sprintf('Error processing order with WebOrderNo %s: %s', $webOrderNo, $e->getMessage()));
            return false;
        } catch (\Exception $e) {
            $this->logger->error(sprintf('Unexpected error processing order with WebOrderNo %s: %s', $webOrderNo, $e->getMessage()));
            return false;
        }
    }

    /**
     * Load order by increment ID
     *
     * @param string $incrementId
     * @return OrderInterface|null
     */
    private function loadOrderByIncrementId(string $incrementId): ?OrderInterface
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('increment_id', $incrementId)
            ->create();

        $orders = $this->orderRepository->getList($searchCriteria)->getItems();

        return !empty($orders) ? reset($orders) : null;
    }
}
