<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="madhat_siteintegrationshipping_shipping" resource="default" engine="innodb" comment="madhat_siteintegrationshipping_shipping Table">
        <column xsi:type="int" name="shipping_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="shipping_id"/>
        </constraint>
        <column name="picklist_no" nullable="false" xsi:type="int" comment="picklist_no" identity="false"/>
        <column name="product_no" nullable="false" xsi:type="varchar" comment="product_no" length="255"/>
        <column name="quantity" nullable="false" xsi:type="int" comment="quantity" identity="false"/>
        <column name="shipment_id" nullable="true" xsi:type="varchar" comment="shipment_id" length="255"/>
        <column name="scanning_timestamp" nullable="true" xsi:type="varchar" comment="scanning_timestamp" length="255"/>
        <column name="shipment_method" nullable="true" xsi:type="varchar" comment="shipment_method" length="255"/>
        <column name="delivery_group" nullable="false" xsi:type="int" comment="delivery_group" identity="false"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Updated At"/>
        <column xsi:type="int" name="order_id" padding="10" unsigned="true" nullable="true" identity="false"
                comment="Order ID"/>
        <column xsi:type="varchar" name="increment_id" nullable="true" length="32" comment="Order Increment ID"/>
        <column xsi:type="int" name="sales_shipment_id" padding="10" unsigned="true" nullable="true" identity="false"
                comment="sales_shipment Entity ID"/>
        <index referenceId="MADHAT_SITEINTEGRATIONSHIPPING_SHIPPING_PICKLIST_NO" indexType="btree">
            <column name="picklist_no"/>
        </index>
        <index referenceId="MADHAT_SITEINTEGRATIONSHIPPING_SHIPPING_PRODUCT_NO" indexType="btree">
            <column name="product_no"/>
        </index>
        <constraint xsi:type="unique" referenceId="MADHAT_SITEINTEGRATIONSHIPPING_SHIPPING_PICKLIST_NO_PRODUCT_NO">
            <column name="picklist_no"/>
            <column name="product_no"/>
        </constraint>
    </table>
</schema>
