<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace MadHat\SiteIntegrationShipping\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface ShippingRepositoryInterface
{

    /**
     * Save Shipping
     * @param \MadHat\SiteIntegrationShipping\Api\Data\ShippingInterface $shipping
     * @return \MadHat\SiteIntegrationShipping\Api\Data\ShippingInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \MadHat\SiteIntegrationShipping\Api\Data\ShippingInterface $shipping
    );

    /**
     * Retrieve Shipping
     * @param string $shippingId
     * @return \MadHat\SiteIntegrationShipping\Api\Data\ShippingInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($shippingId);

    /**
     * Retrieve Shipping matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \MadHat\SiteIntegrationShipping\Api\Data\ShippingSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Shipping
     * @param \MadHat\SiteIntegrationShipping\Api\Data\ShippingInterface $shipping
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \MadHat\SiteIntegrationShipping\Api\Data\ShippingInterface $shipping
    );

    /**
     * Delete Shipping by ID
     * @param string $shippingId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($shippingId);
}

