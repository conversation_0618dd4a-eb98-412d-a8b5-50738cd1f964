<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="makewebo_cache_analyst" resource="default" comment="List of URL codes">
        <column xsi:type="int" name="id" identity="true" comment="Record Id" />
        <column xsi:type="varchar" name="event_name" length="150" nullable="false" comment="Event Name" />
        <column xsi:type="varchar" length="255" name="class_name" nullable="false" comment="Class Name"/>
        <column xsi:type="text" name="tags"  nullable="false"  comment="Tags" />
        <column xsi:type="int" name="tags_count"  nullable="false" default="0"  comment="Tags Count" />
        <column xsi:type="smallint" name="need_sync"  nullable="false" default="0"  comment="Need Sync" />
        <column xsi:type="timestamp" name="created_at" on_update="false" default="CURRENT_TIMESTAMP" comment="Created At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id" />
        </constraint>

        <index referenceId="MAKEWEBO_CACHE_ANALYST_ID" indexType="btree">
            <column name="id"/>
        </index>
        <index referenceId="MAKEWEBO_CACHE_ANALYST_EVENT_NAME" indexType="btree">
            <column name="event_name"/>
        </index>
        <index referenceId="MAKEWEBO_CACHE_ANALYST_CLASS_NAME" indexType="btree">
            <column name="class_name"/>
        </index>
        <index referenceId="MAKEWEBO_CACHE_ANALYST_CREATED_AT" indexType="btree">
            <column name="created_at"/>
        </index>
    </table>
</schema>
