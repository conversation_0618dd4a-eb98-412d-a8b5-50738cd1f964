<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="cache_analyst" translate="label" type="text" sortOrder="1000" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Cache Analyst (Varnish)</label>
            <tab>madhat</tab>
            <resource>Makewebo_CacheAnalyst::config</resource>
            <group id="settings" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Logging</label>
                <field id="enable" translate="label comment" type="select" sortOrder="2" showInDefault="1" showInWebsite="0" showInStore="0" canRestore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="clean_by_cron" translate="label comment" type="select" sortOrder="3" showInDefault="1" showInWebsite="0" showInStore="0" canRestore="1">
                    <label>Clean tags by Cron</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="start_time" translate="label comment" type="text" sortOrder="4" showInDefault="1" showInWebsite="0" showInStore="0" canRestore="1">
                    <label>Start Time</label>
                    <comment>It will clean tags by cron, which generated from Start Time.</comment>
                    <frontend_model>Makewebo\CacheAnalyst\Model\System\Config\Time</frontend_model>
                </field>
                <field id="end_time" translate="label comment" type="text" sortOrder="5" showInDefault="1" showInWebsite="0" showInStore="0" canRestore="1">
                    <label>End Time</label>
                    <comment>It will clean tags by cron, which generated till End Time.</comment>
                    <frontend_model>Makewebo\CacheAnalyst\Model\System\Config\Time</frontend_model>
                </field>
                <field id="clean_log_before_days" translate="label" type="text" sortOrder="6" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Clean Log Before Days</label>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
