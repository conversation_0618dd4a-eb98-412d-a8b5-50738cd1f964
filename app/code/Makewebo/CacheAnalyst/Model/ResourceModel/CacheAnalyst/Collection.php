<?php

namespace Make<PERSON><PERSON>\CacheAnalyst\Model\ResourceModel\CacheAnalyst;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Makewebo\CacheAnalyst\Model\CacheAnalyst as Model;
use Makewebo\CacheAnalyst\Model\ResourceModel\CacheAnalyst as ResourceModel;

class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_eventPrefix = 'makewebo_cache_analyst_collection';

    /**
     * Initialize collection model.
     */
    protected function _construct()
    {
        $this->_init(Model::class, ResourceModel::class);
    }
}
