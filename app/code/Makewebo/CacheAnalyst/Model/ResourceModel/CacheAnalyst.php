<?php

namespace Makewebo\CacheAnalyst\Model\ResourceModel;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Model\ResourceModel\Db\AbstractDb;

class CacheAnalyst extends AbstractDb
{
    /**
     * @var string
     */
    protected $_eventPrefix = 'makewebo_cache_analyst_resource_model';

    /**
     * Initialize resource model.
     */
    protected function _construct()
    {
        $this->_init('makewebo_cache_analyst', 'id');
        $this->_useIsObjectNew = true;
    }

    /**
     * @throws LocalizedException
     */
    public function deleteCacheLogBeforeDays(int $days): void
    {
        $where = 'created_at < now() - interval ' . $days .' day';
        $this->getConnection()->delete($this->getMainTable(), $where);
    }
}
