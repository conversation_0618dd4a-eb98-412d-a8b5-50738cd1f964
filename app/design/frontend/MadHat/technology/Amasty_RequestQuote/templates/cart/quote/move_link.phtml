<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Request a Quote Hyva Compatibility
 */

use Amasty\RequestQuote\Block\Cart\Quote\Move;
use Magento\Framework\Escaper;

/** @var Move $block */
/** @var Escaper $escaper */

$formData = json_decode($block->getPostData(), true);
?>
<form action="<?= $escaper->escapeUrl($formData['action']) ?>" method="post">
    <?= $block->getBlockHtml('formkey'); ?>
    <input type="hidden" name="data" value='<?= /** @noEscape */ json_encode($formData['data']) ?>'/>
    <div class="quote-section py-7 px-6">
        <p class="text-sm mb-5"><?= __('Are you a') ?> <a href="#" class="font-semibold underline hover:no-underline"><?= __('business customer?') ?></a> <?= __('For larger procurements, please contact us for a quote and a proposed solution. One of our sales representatives will get back to you shortly.') ?></p>
        <div class="quote-actions mx-6">
            <button type="submit"
                    class="amquote-addto-button amasty-quote-link w-full btn btn-tertiary flex text-sm min-h-[52px] font-medium py-3 px-10  justify-center text-center"
                    title="<?= $escaper->escapeHtmlAttr(__('Request a Quote')) ?>">
                <?= $escaper->escapeHtml(__('Request a Quote')) ?>
            </button>
        </div>
    </div>
</form>
