<?php
/**
 * BSS Commerce Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://bsscommerce.com/Bss-Commerce-License.txt
 *
 * @category  BSS
 * @package   Hyva_BssSimpledetailconfigurable
 * <AUTHOR> Team
 * @copyright Copyright (c) 2023-present BSS Commerce Co. ( http://bsscommerce.com )
 * @license   http://bsscommerce.com/Bss-Commerce-License.txt
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\BssSimpledetailconfigurable\ViewModel\Helper as ModuleViewModel;
use MadHat\InventoryImport\Model\Source\ProductStatus;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var Product $product */
$product = $this->hasData('product')
    ? $this->getData('product')
    : $currentProduct->get();

$moduleViewModel = $viewModels->require(ModuleViewModel::class);
$moduleConfig = $moduleViewModel->getModuleConfig();
if (!$product || !$product->getId()) {
    return;
}
$inStock = __('In stock');
$outOfStock = __('Out of stock');
$product->getTypeId();
?>
<?php if ($block->getParentBlock()->displayProductStockStatus()): ?>
    <div class="">
        <p class="">

        </p>
        <?php if ($product->getTypeId() == 'configurable' && $moduleConfig->isShowName() && $moduleConfig->isModuleEnable()): ?>
            <p class="flex items-center available gap-x-2 stock"
               :class="{ 'available': stock_status, 'unavailable': !stock_status }"
               title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>"
               x-data="{inventory_status: '', inventory_status_flag: 0, stock_status: <?= $product->getIsSalable() ? '1' : '0' ?>}">
                <span @simple-detail-product-active.window="inventory_status_flag = $event.detail.event_product_data.inventory_status_flag"
                      class="w-3 h-3 rounded-full shrink-0"
                      :class="{ 'bg-green-500': inventory_status_flag == 1, 'bg-yellow-500': inventory_status_flag == 2, 'bg-red-500': !inventory_status_flag }"></span>
                <span @simple-detail-product-active.window="inventory_status = $event.detail.event_product_data.inventory_status"
                      x-text="inventory_status"></span>
            </p>
        <?php else: ?>
            <?php $madhatInventoryStatus = (string) $product->getAttributeText('madhat_inventory_status'); ?>
            <?php if ($product->getIsSalable()): ?>
                <?php if ($madhatInventoryStatus == ProductStatus::LABEL_IN_STOCK): ?>
                    <p class="flex items-center available gap-x-2 stock"
                       title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
                        <span class="w-3 h-3 bg-green-500 rounded-full shrink-0"></span>
                        <span><?= /* @noEscape */ $madhatInventoryStatus;?></span>
                    </p>
                <?php elseif ($madhatInventoryStatus == ProductStatus::LABEL_AVAILABLE_ON_DEMAND
                    || $madhatInventoryStatus == ProductStatus::LABEL_AVAILABLE_ON_PREORDER
                    || $madhatInventoryStatus == ProductStatus::LABEL_ETA_2_DAYS
                    || $madhatInventoryStatus == ProductStatus::LABEL_ETA_3_DAYS
                    || $madhatInventoryStatus == ProductStatus::LABEL_ETA_7_DAYS
                    || $madhatInventoryStatus == ProductStatus::LABEL_ETA_14_DAYS
                    || $madhatInventoryStatus == ProductStatus::LABEL_ETA_30_DAYS
                ): ?>
                    <p class="flex items-center available gap-x-2 stock"
                       title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
                        <span class="w-3 h-3 bg-yellow-500 rounded-full shrink-0"></span>
                        <span><?= /* @noEscape */ $madhatInventoryStatus;?></span>
                    </p>
                <?php endif; ?>
            <?php else: ?>
                <?php if ($madhatInventoryStatus == ProductStatus::LABEL_IN_STOCK): ?>
                    <p class="flex items-center align-middle gap-x-2 unavailable stock"
                       title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
                        <span class="w-3 h-3 bg-red-500 rounded-full shrink-0"></span>
                        <span><?= /* @noEscape */ __(ProductStatus::LABEL_OUT_OF_STOCK);?></span>
                    </p>
                <?php else: ?>
                    <p class="flex items-center align-middle gap-x-2 unavailable stock"
                       title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
                        <span class="w-3 h-3 bg-red-500 rounded-full shrink-0"></span>
                        <span><?= /* @noEscape */ __($madhatInventoryStatus);?></span>
                    </p>
                <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>
    </div>
<?php endif; ?>

