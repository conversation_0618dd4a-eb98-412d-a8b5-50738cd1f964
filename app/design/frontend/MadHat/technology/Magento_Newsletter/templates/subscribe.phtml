<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Framework\Escaper;
use Magento\Newsletter\Block\Subscribe;

/** @var Subscribe $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var ReCaptcha $recaptcha */
/** @var HeroiconsOutline $heroicons */

$heroicons = $viewModels->require(HeroiconsOutline::class);

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaNewsletter module
$recaptcha = $block->getData('viewModelRecaptcha');
?>
<div class="">
    <div class="text-xl leading-5 font-semibold uppercase text-cgrey-0">
        Newsletter
    </div>
    <div class="text-sm mt-4">
        <?= $escaper->escapeHtml(__('Subscribe for the latest news and our best offers.')) ?>
    </div>
</div>
<form
    class="mt-5 mb-8 md:flex flex-col lg:max-w-2xl lg:mx-auto"
    action="<?= $escaper->escapeUrl($block->getFormActionUrl()) ?>"
    method="post"
    x-data="initNewsletterForm()"
    @submit.prevent="submitForm()"
    id="newsletter-validate-detail"
    aria-label="<?= $escaper->escapeHtmlAttr(__('Subscribe to Newsletter')) ?>"
>
    <input
        name="email"
        type="email"
        required
        id="newsletter-subscribe"
        :class="['formMessage', statusClass]"
        class="newsletter-email-input"
        placeholder=""
        aria-describedby="footer-newsletter-heading"
    >
    <label class="label"
           for="newsletter-subscribe"
           aria-label="<?= $escaper->escapeHtmlAttr(__('Enter your email')) ?>"
    >
        <?= $escaper->escapeHtml(__('Email address')) ?>
    </label>
    <?= $block->getBlockHtml('formkey') ?>
    <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_NEWSLETTER) : '' ?>
    <div>
        <template x-if="displayErrorMessage">
            <p class="flex items-center text-red">
                    <span class="inline-block w-8 h-8 mr-3">
                        <?= $heroicons->exclamationCircleHtml('', 24, 24, ['aria-hidden' => 'true']) ?>
                    </span>
                <template x-for="errorMessage in errorMessages">
                    <span x-html="errorMessage"></span>
                </template>
            </p>
        </template>
    </div>

    <button class="p-3 mt-4 cursor-pointer shadow-sm btn btn-primary rounded-none btn-subscribe md:mx-0">
        <?= $escaper->escapeHtml(__('Subscribe')) ?>
    </button>
    <div class="newsletter-message">
        <div x-html="formMessage"
            :class="['formMessage', statusClass]">
        </div>
    </div>
</form>
<div class="w-full">
    <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_NEWSLETTER) : '' ?>
</div>
<script>
    function initNewsletterForm() {
        return {
            errors: 0,
            hasCaptchaToken: 0,
            displayErrorMessage: false,
            errorMessages: [],
            setErrorMessages(messages) {
                this.errorMessages = [messages]
                this.displayErrorMessage = this.errorMessages.length
            },
            formData: {
                email: '',
                form_key: ''
            },
            formMessage: '',
            statusClass: '',
            submitForm() {
                this.formMessage = '';
                this.statusClass = '';
                // Do not rename $form, the variable is expected to be declared in the recaptcha output
                const $form = document.querySelector('#newsletter-validate-detail');

                <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_NEWSLETTER) : '' ?>

                if (this.errors === 0) {
                    const formUenc = hyva.getUenc();
                    const formData = new FormData($form);
                    let bodyUrl = new URLSearchParams(formData);
                    bodyUrl.append("uenc", formUenc);

                    const postUrl = BASE_URL + 'newsletter/subscriber/new/';

                    fetch(postUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: bodyUrl.toString(),
                    }).then(response => {
                        if (response.ok) {
                            return response.json();
                        } else {
                            console.warn("request failed", response);
                        }
                    }).then(data => {
                        if (data) {
                            this.formMessage = data.message;
                            this.statusClass = data.status === "success" ? "success" : "error";

                            if (this.statusClass === "success") {
                                setTimeout(() => {
                                    this.formMessage = '';
                                    this.statusClass = '';
                                }, 5000);
                            }
                        }
                    });
                }
            }
        }
    }
</script>
