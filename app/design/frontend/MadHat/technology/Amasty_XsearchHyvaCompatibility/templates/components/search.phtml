<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Advanced Search Hyva Compatibility M2 by Amasty
 */

declare(strict_types=1);

use Amasty\XsearchHyvaCompatibility\ViewModel\ComponentsRegistry;
use Amasty\XsearchHyvaCompatibility\ViewModel\SearchPopup;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var ComponentsRegistry $viewModelPosts */

$componentRegistry = $viewModels->require(ComponentsRegistry::class);
$popupViewModel = $viewModels->require(SearchPopup::class);
$blocks = $popupViewModel->getBlockPositions();
?>

<?= /** @noEscape */ $componentRegistry->renderOnce('search-js-products') ?>
<?= /** @noEscape */ $componentRegistry->renderOnce('search-js-autocomplete') ?>
<?= /** @noEscape */ $componentRegistry->renderOnce('search-js-carousel') ?>

<div class="relative py-2 text-black z-30 container" x-data="Object.assign({amXsearchCarouselComponent}, amXsearchProductsComponent(), amXsearchAutocompleteComponent())" x-init="dynamicLayout($el)">

    <?= /** @noEscape */  $componentRegistry->render('loading') ?>

    <div x-show="showOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-20 w-full h-full top-0 left-0 right-0 bottom-0"
         @click.prevent="closeSearch()"
         @keydown.window.escape="closeSearch()">
    </div>

    <div class="form mini-search z-40 relative right-0">
        <form id="search_mini_form_widget" @submit.prevent="redirect()"
              action="<?= $escaper->escapeUrl($popupViewModel->getResultsPageUrl()) ?>"
              class="flex justify-end" method="GET">
            <label class="hidden" for="search" data-role="mini-search-label">
                <span><?= $escaper->escapeHtml(__('Search')) ?></span>
            </label>
            <div class="w-full flex align-center justify-end"
                 :style="`max-width: ${!isDefaultSearchInput && !isFullWidthSearch ? searchPopupWidth + 'px' : '100%'}`">
                <input id="search_input_widget"
                       x-ref="searchInput"
                       maxlength="<?= $escaper->escapeHtml($popupViewModel->getMaxQueryLength()) ?>"
                       form="search_mini_form_widget"
                       type="search"
                       name="<?= $escaper->escapeHtml($popupViewModel->getQueryParamName()) ?>"
                       value="<?= $escaper->escapeHtml($popupViewModel->getQueryText()) ?>"
                       autocomplete="off"
                       placeholder="<?= $escaper->escapeHtmlAttr(__('Search entire store here...')) ?>"
                       class="p-2 text-lg leading-normal transition appearance-none text-grey-800 relative z-20
                    focus:outline-none focus:border-transparent lg:text-xl flex-am-search-sidebar"
                       @focus="doSearch()"
                       @search="checkSearchInput()"
                       @input.debounce.<?= $escaper->escapeHtml($popupViewModel->getSearchInputDelayInMS()) ?>="doSearch()"
                       @keydown.arrow-down.prevent="focusElement($el.querySelector('[tabindex]'))" />
                <?php if ($popupViewModel->showSearchButton()): ?>
                    <button type="submit" title="<?= $escaper->escapeHtml(__('Search')) ?>"
                            class="am-custom-search-button  h-full action search w-auto btn btn-primary justify-center text-sm mb-3 md:mb-0 mr-auto rounded-none"
                            x-bind:disabled="isLoading || latestQuery.length < minSearchLength"
                            :class="{
                                'opacity-50': isLoading || latestQuery.length < minSearchLength,
                                'am-custom-search-button': <?= $escaper->escapeHtml($popupViewModel->isCustomLayoutEnabled() ? 'true' : 'false') ?>,
                            }"
                            aria-label="Search">
                        <?= $escaper->escapeHtml(__('Search')) ?>
                    </button>
                <?php endif; ?>
            </div>
        </form>
        <template x-if="searchInitialized">
            <div
                class="w-full -right-position -bottom-position right-0 top-15 bg-white shadow-lg max-h-screen-75 overflow-auto text-sm amsearch-container"
                :class="{'w-full absolute': !config.isFullScreenMode}"
                tabindex="-1"
                x-cloak
                :style="`${!isDefaultSearchInput && !isFullWidthSearch ? 'max-width:' + searchPopupWidth + 'px' : ''}`"
                x-show="!isLoading && showOverlay">
                <div class="flex w-full content-start flex-wrap box-border relative"
                    :class="{
                            'flex-col': true,
                            'flex-col lg:flex-row': !isHorizontalView,
                         }">
                    <div class="w-full lg:border-r pt-6 am-search-sidebar"
                         :class="{
                            'border-gray-600': showSideBar() && getResultProductsCount() > 0,
                            'hidden': latestQuery.length < minSearchLength && (
                                !showOnFirstClick('recentSearches') && !showOnFirstClick('popularSearches') && !showOnFirstClick('browsingHistory')
                            ) || isSidebarSectionsDisabled(),
                            'am-search-horizontal-view': isHorizontalView,
                         }">
                        <?php foreach ($blocks as $blockName => $position): ?>
                            <template x-if="<?= $escaper->escapeHtml($blockName === 'products' ? 'isHorizontalView && !noResultsFound && ' : '') ?>
                                        sections?.<?= $escaper->escapeHtml($blockName) ?>?.items?.length">
                                <div x-show="(latestQuery.length >= minSearchLength || showOnFirstClick('<?= $escaper->escapeHtml($blockName) ?>'))" x-cloak>
                                    <?= /** @noEscape */ $componentRegistry->render('search-' . $blockName) ?>
                                </div>
                            </template>
                        <?php endforeach; ?>
                    </div>
                    <div class="relative"
                         :class="{
                            'am-search-content p-2 pt-6': latestQuery.length >= minSearchLength || (
                                showOnFirstClick('recentSearches') || showOnFirstClick('popularSearches') || showOnFirstClick('browsingHistory')
                            ),
                            'w-full': latestQuery.length < minSearchLength && (
                                !showOnFirstClick('recentSearches') && !showOnFirstClick('popularSearches') && !showOnFirstClick('browsingHistory')
                            ),
                            'am-search-horizontal-view': isHorizontalView,
                            'p-2 pt-6': slider.recentlyViewed || slider.bestsellers,
                            'am-search-sidebar-disabled': isSidebarSectionsDisabled(),
                         }">
                        <div class="flex flex-nowrap flex-col" x-show="latestQuery.length < minSearchLength" x-cloak>
                            <?php if ($popupViewModel->isRecentlyViewedEnabled()): ?>
                                <template x-if="slider.recentlyViewed">
                                    <?= /** @noEscape */ $componentRegistry->render('search-recentlyViewed') ?>
                                </template>
                            <?php endif; ?>

                            <?php if ($popupViewModel->isBestsellersEnabled()): ?>
                                <template x-if="slider.bestsellers">
                                    <?= /** @noEscape */ $componentRegistry->render('search-bestsellers') ?>
                                </template>
                            <?php endif; ?>
                        </div>

                        <template x-if="!noResultsFound && !isHorizontalView && latestQuery.length >= minSearchLength">
                            <?= /** @noEscape */ $componentRegistry->render('search-products') ?>
                        </template>

                        <div class="p-4 box-border w-full h-full flex" x-show="noResultsFound" x-cloak>
                            <div
                                class="text-center flex align-center w-full text-xl justify-center content-center flex-row bg-gray-100 items-center flex-wrap h-full max-h-[90vh] word-break"
                                x-html="'<?= $escaper->escapeHtml(__('😔 We could not find anything for&nbsp;<strong>"%s"</strong>'), ['strong']) ?>'.replace('%s', latestQuery)">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</div>
