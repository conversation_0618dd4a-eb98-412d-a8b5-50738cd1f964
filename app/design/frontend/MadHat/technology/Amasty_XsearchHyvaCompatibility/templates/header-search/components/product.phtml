<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Advanced Search Hyva Compatibility M2 by Amasty
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductPrice;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Amasty\XsearchHyvaCompatibility\ViewModel\SearchPopup;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Wishlist $wishlistViewModel */
$wishlistViewModel = $viewModels->require(Wishlist::class);
/** @var ProductCompare $compareViewModel */
$compareViewModel = $viewModels->require(ProductCompare::class);
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
/** @var ProductPrice $productPrice */
$productPrice = $viewModels->require(ProductPrice::class);
/** @var SearchPopup $popupViewModel */
$popupViewModel = $viewModels->require(SearchPopup::class);
// phpcs:disable Generic.Files.LineLength.TooLong
?>

<div class="relative max-w-full w-[48%] lg:w-[32.3%] p-1 bg-cgrey-30 border border-cgrey-30 item product product-item card card-interactive flex round-none shadow-none border-none"
     :class="{
        '': !config.isGridView,
        'mb-5 list-view': config.isGridView
                                    && (isFullWidthSearch || isDefaultSearchInput || searchPopupWidth > 900),
        'flex-col mt-2': !config.isGridView
                                    && (!isFullWidthSearch || !isDefaultSearchInput || searchPopupWidth <= 900)
     }">
    <a :href="getProductUrl(product)"
       class="photo product-item-photo block mb-3 flex justify-center align-center h-36 w-full bg-white"
       tabindex="-1"
       <?php if ($popupViewModel->isProductLabelEnabled()): ?>x-ref="amLabelParent"<?php endif; ?>
       :class="{'': config.isGridView}"
    >
        <img class="hover:shadow-sm object-contain max-w-full max-h-full"
             :src="getProductImageUrl(product)"
             :alt="product.small_image.label"
             <?php if ($popupViewModel->isProductLabelEnabled()): ?>x-ref="amLabelImgParent"<?php endif; ?>
             width="144"
        />

        <?php if ($popupViewModel->isProductLabelEnabled()): ?>
            <?= /* @noEscape */ $block->fetchView($block->getTemplateFile('Amasty_ProductLabelAdvancedSearch::components/product-wrapper.phtml')) ?>
        <?php endif; ?>
    </a>
    <div
        x-data="{regularPriceValue : product.price_range.maximum_price.regular_price.value, finalPriceValue: product.price_range.minimum_price.final_price.value, discountPercentage: ((product.price_range.maximum_price.regular_price.value - product.price_range.minimum_price.final_price.value) / product.price_range.maximum_price.regular_price.value) *100}"
    >
        <template
            x-if="regularPriceValue > 0 && discountPercentage > 0"
        >
            <div
                class="absolute top-0 left-0 flex justify-center items-center bg-cred text-xs font-semibold text-cgrey-0 min-w-14 min-h-7 px-2">
                <span> - <strong x-html="parseFloat(discountPercentage).toFixed(2)"></strong> % </span>
            </div>
        </template>
    </div>
    <div class="product-info flex flex-col grow">
        <div class="mt-2 mb-1 items-center justify-center text-primary text-lg"
             :class="{'md:text-left': config.isGridView, '': !config.isGridView}"
        >
            <div class="Xsearch-madhat-brand">
                <span x-html="product.madhat_brand" class="amsearch-product-madhat-brand"></span>
            </div>
            <a class="product-item-link action link"
               :href="getProductUrl(product)"
            >
                <span x-html="product.name" class="amsearch-product-name"></span>
            </a>
            <template x-if="config.isShowSku">
                <div class="text-sm font-normal">
                    <span><?= $escaper->escapeHtml(__('SKU')) ?>:</span>
                    <span x-html="product.sku"></span>
                </div>
            </template>
        </div>
        <template x-if="config.isProductReviewsBlockEnabled">
            <?= /* @noEscape */ $block->fetchView(
                $block->getTemplateFile('Amasty_Xsearch::header-search/components/rating.phtml')
            ) ?>
        </template>
        <!-- <div class="mt-2 mb-1 items-center justify-center text-primary"
             :class="{'text-left': config.isGridView, 'text-center': !config.isGridView}"
             x-html="truncateWithDots(product.short_description.html, config.shortDescriptionLength)"
        ></div> -->
        <div class="price-container flex flex-col justify-center items-center">
            <div class="final-price">
                <span class="price-wrapper title-font font-medium text-2xl text-gray-900">
                    <span class="price"
                          x-html="hyva.formatPrice(product.price_range.minimum_price.final_price.value)">
                    </span>
                </span>

                <span class="old-price sly-old-price no-display block"
                      x-show="product.price_range.minimum_price.final_price.value < product.price_range.maximum_price.regular_price.value"
                >
                    <span class="price-container price-final_price tax weee block text-center">
                        <span
                            class="text-cgrey-80 text-xs line-through text-xs-[1px] price-wrapper product-price-out flex flex-wrap"
                            x-html="hyva.formatPrice(product.price_range.maximum_price.regular_price.value)"
                        >
                        </span>
                    </span>
                </span>
            </div>
            <?php if ($productPrice->displayPriceInclAndExclTax()): ?>
                <template x-if="product.price_range.minimum_price.regular_price">
                    <div class="final-price-excl-tax">
                        <span class="font-regular text-gray-900">
                            <span><?= $escaper->escapeHtml(__('Excl. Tax')) ?>:</span>
                            <span class="price" x-html="hyva.formatPrice(product.price_range.minimum_price.regular_price.value)"></span>
                        </span>
                    </div>
                </template>
            <?php endif; ?>
        </div>
        <div class="pt-3 flex items-center"
             :class="{
             'mt-auto justify-center': !config.isGridView ,
             'flex-wrap': config.isGridView && config.isProductAddToCartBlockEnabled && product.is_salable,
             'justify-start': config.isGridView
             }">
            <template x-if="config.isProductAddToCartBlockEnabled">
                <button type="button"
                        x-on:click.prevent="addToCart(product, $event)"
                        class="btn btn-primary justify-center text-sm"
                        :class="{
                        'w-auto': !config.isGridView,
                        'w-full sm:w-auto mb-3 sm:mb-0': config.isGridView,
                        'disable': !product.is_salable
                        }"
                        :disabled="!product.is_salable"
                >
                    <span><?= $escaper->escapeHtml(__('Add to Cart')) ?></span>
                </button>
            </template>
        </div>
    </div>
</div>
