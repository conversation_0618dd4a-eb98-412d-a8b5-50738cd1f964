<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Catalog\Block\Product\View;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;

/** @var View $block */
/** @var Escaper $escaper */

/** @var Product $product */
$product = $block->getProduct();

/**
 * Add to cart url is set on 'product.info' block by
 * @see \Magento\Checkout\Block\Cart\Item\Configure::_prepareLayout
 */
$productInfoBlock = $block->getLayout()->getBlock('product.info');

?>

<form method="post"
      action="<?= $escaper->escapeUrl($productInfoBlock->getSubmitUrl($product)) ?>"
      class=""
      id="product_addtocart_form"<?php if ($product->getOptions()): ?> enctype="multipart/form-data"<?php endif; ?>
>
    <input type="hidden" name="product" value="<?= (int)$product->getId() ?>" />
    <input type="hidden" name="selected_configurable_option" value=""  />
    <input type="hidden" name="related_product" id="related-products-field" value="" />
    <input type="hidden" name="item"  value="<?= (int)$block->getRequest()->getParam('id') ?>">
    <?= $block->getBlockHtml('formkey') ?>

    <?= $block->getChildHtml() ?>

</form>
