<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Catalog\Helper\Output as CatalogOutputHelper;
use Magento\Catalog\Block\Product\View\Attributes;
use Magento\Catalog\Model\Product;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Escaper $escaper */
/** @var CatalogOutputHelper $output */
/** @var Attributes $block */

/** @var CatalogOutputHelper $catalogOutputHelper */
$catalogOutputHelper = $this->helper(CatalogOutputHelper::class);

/** @var Product $product */
$product = $block->getProduct();

$getLayoutStyle = (string) $block->getLayoutStyle() ?: "";

$layoutStyleTableRow = $getLayoutStyle === "" ? "block lg:table-row" : ($getLayoutStyle === "list" ? "block" : "");
$layoutStyleTableCellHeading = $getLayoutStyle === ""
    ? "block lg:table-cell pt-3 lg:py-2 px-3"
    : ($getLayoutStyle === "list" ? "block pt-3 px-3" : "py-2 px-3");
$layoutStyleTableCellItem = $getLayoutStyle === ""
    ? "block lg:table-cell pb-3 lg:py-2 px-3"
    : ($getLayoutStyle === "list" ? "block pb-3 px-3" : "py-2 px-3");
?>

<?php if ($attributes = $block->getAdditionalData()): ?>
    <div x-data="productAttributesLimit()" class="table-wrapper overflow-x-auto" id="product-attributes">
        <table class="additional-attributes table-fixed w-full">
            <?php $i = 0; ?>
            <?php foreach ($attributes as $attribute): ?>
                <tr x-show="showAttributeRow(<?= $i ?>)" class="border-b border-cgrey-40 last:border-b-0 bg-white odd:bg-cgrey-40 <?= $layoutStyleTableRow ?>">
                    <th
                        class="col label product-attribute-label text-start text-black font-semibold break-words hyphens-auto <?= $layoutStyleTableCellHeading ?>"
                        scope="row"
                    ><?= $escaper->escapeHtml($attribute['label']) ?></th>
                    <td
                        class="col data product-attribute-value text-start text-cgrey-65 break-words hyphens-auto <?= $layoutStyleTableCellItem ?>"
                        data-th="<?= $escaper->escapeHtmlAttr($attribute['label']) ?>"
                    ><?= /* @noEscape */ $catalogOutputHelper->productAttribute($product, $attribute['value'], $attribute['code']) ?></td>
                </tr>
            <?php $i++; endforeach; ?>
        </table>
        <div class="flex items-center justify-center mt-10">
            <button
                @click="open = ! open"
                x-text="open ? 'Show less' : 'Show more'"
                class="btn btn-size-md bg-black hover:bg-grey-65"
                ></button>
        </div>
    </div>
<?php endif;?>

<script>
    function productAttributesLimit() {
        return {
            attributesLimit: 2,
            open: true,
            showAttributeRow(i) {
                if (!this.open) {
                    return this.attributesLimit > i;
                }

                return this.open;
            },
        }
    }
</script>
