<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Checkout\Block\Cart\Item\Renderer\Actions\Edit;
use Magento\Framework\Escaper;

/** @var Edit $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsSolid $heroicons */
$heroicons = $viewModels->require(HeroiconsSolid::class);
$item = $block->getItem();

if ($item->getProductType() === 'configurable') {
    $productOptions = $item->getProduct()->getTypeInstance()->getOrderOptions($item->getProduct());
    $productName = $productOptions["simple_name"];
} else {
    $productName = $item->getName();
}
?>
<?php if ($block->isProductVisibleInSiteVisibility()): ?>
    <a
        class="text-cgrey-65 flex justify-center items-center action action-edit inline-flex size-5 hidden"
        href="<?= $escaper->escapeUrl($block->getConfigureUrl()) ?>"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Edit %1', $productName)) ?>"
    >
        <?= $heroicons->pencilHtml('min-h-5 min-w-5', 24, 24, ['aria-hidden' => 'true']) ?>
    </a>
<?php endif ?>
