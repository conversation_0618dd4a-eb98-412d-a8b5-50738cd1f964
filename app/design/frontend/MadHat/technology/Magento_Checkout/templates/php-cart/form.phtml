<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

// phpcs:disable Magento2.Templates.ThisInTemplate

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Cart\ItemOutput;
use Hyva\Theme\ViewModel\Modal;
use Magento\Checkout\Block\Cart\Grid;
use Magento\Checkout\ViewModel\Cart as CartViewModel;
use Magento\Framework\Escaper;

/** @var Grid $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var CartViewModel $cartViewModel */
$cartViewModel = $viewModels->require(CartViewModel::class);

/** @var Modal $modalViewModel */
$modalViewModel = $viewModels->require(Modal::class);

/** @var ItemOutput $cartItemOutputViewModel */
$cartItemOutputViewModel = $viewModels->require(ItemOutput::class);
?>
<?php $mergedCells = ($cartItemOutputViewModel->isItemPriceDisplayBoth() ? 2 : 1); ?>
<?= $block->getChildHtml('form_before') ?>
<form action="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/updatePost')) ?>"
      x-data="{}"
      @submit.prevent="hyva.postCart($event.target)"
      method="post"
      id="form-validate"
      class="form form-cart w-full float-left"
>
    <?= $block->getBlockHtml('formkey') ?>
    <div class="cart table-wrapper<?= $mergedCells == 2 ? ' detailed' : '' ?>">
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-top toolbar">
                <?= $block->getPagerHtml() ?>
            </div>
        <?php endif ?>
        <table id="shopping-cart-table"
               class="cart items data table w-full table-row-items"
        >
            <caption class="table-caption sr-only">
                <?= $escaper->escapeHtml(__('Shopping Cart Items')) ?>
            </caption>
            <thead class="hidden">
                <tr class="text-right">
                    <th class="col item text-left pt-4 px-4 pb-2" scope="col">
                        <?= $escaper->escapeHtml(__('Item')) ?>
                    </th>
                    <th class="col price pt-4 px-4 pb-2" scope="col">
                        <?= $escaper->escapeHtml(__('Price')) ?>
                    </th>
                    <th class="col qty pt-4 px-4 pb-2" scope="col">
                        <?= $escaper->escapeHtml(__('Qty')) ?>
                    </th>
                    <th class="col subtotal pt-4 px-4 pb-2" scope="col">
                        <?= $escaper->escapeHtml(__('Subtotal')) ?>
                    </th>
                </tr>
            </thead>
            <?php foreach ($block->getItems() as $item): ?>
                <?= $block->getItemHtml($item) ?>
            <?php endforeach ?>
        </table>
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-bottom toolbar">
                <?= $block->getPagerHtml() ?>
            </div>
        <?php endif ?>
    </div>
    <div class="cart actions flex flex-col sm:flex-row justify-end gap-4 items-center my-4 hidden">
        <?php if ($cartViewModel->isClearShoppingCartEnabled()): ?>
        <script>
            function initClearShoppingCartModal() {
                return Object.assign(
                    hyva.modal(),
                    {
                        postData: {
                            action: '<?= $escaper->escapeUrl($block->getUrl('checkout/cart/updatePost')) ?>',
                            data: {update_cart_action: 'empty_cart'}
                        }
                    }
                );
            }
        </script>
            <div x-data="initClearShoppingCartModal()">
                <?= /** @noEscape */ ($confirmation = $modalViewModel
                    ->confirm(__('Are you sure?'))
                    ->withDetails(__('Are you sure you want to remove all items from your shopping cart?'))
                ) ?>
                <button @click="<?= /** @noEscape */ $confirmation->getShowJs() ?>.then(result => result && hyva.postForm(postData))"
                        type="button" title="<?= $escaper->escapeHtmlAttr(__('Clear Shopping Cart')) ?>"
                        class="action clear" id="empty_cart_button">
                    <span><?= $escaper->escapeHtml(__('Clear Shopping Cart')) ?></span>
                </button>
            </div>

        <?php endif ?>
        <button type="submit"
                name="update_cart_action"
                data-cart-item-update=""
                value="update_qty"
                title="<?= $escaper->escapeHtmlAttr(__('Update Shopping Cart')) ?>"
                class="action update btn btn-secondary"
        >
            <?= $escaper->escapeHtml(__('Update Shopping Cart')) ?>
        </button>
    </div>
</form>
<?= $block->getChildHtml('checkout.cart.order.actions') ?>
<?= $block->getChildHtml('shopping.cart.table.after') ?>
