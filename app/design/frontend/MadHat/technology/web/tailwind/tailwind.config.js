const {
  spacing
} = require('tailwindcss/defaultTheme');

const colors = require('tailwindcss/colors');

const hyvaModules = require('@hyva-themes/hyva-modules');

const ppnColor = {
  blueDarker: '#169DDB',
  blue: '#17ACEF',
  blueLighter: '#5cc5f4',
  orange: '#FF8800',
  red: '#DF1414',
  yellow: '#DBDF69',
  green: '#3AC174',
  greenDarker: '#238726',
}
const ppnGray = {
  90: '#1F2229',
  80: '#4E6174',
  75: '#B8B8B8',
  70: '#778899',
  65: '#707070',
  60: '#C7D2DE',
  50: '#E6EAEE',
  40: '#F2F5F8',
  30: '#F5F5F5',
  0: '#FFFFFF',
}

module.exports = hyvaModules.mergeTailwindConfig({
  theme: {
    extend: {
      screens: {
        'xs': '430px',
        // => @media (min-width: 320px) { ... }
        'sm': '640px',
        // => @media (min-width: 640px) { ... }
        'md': '768px',
        // => @media (min-width: 768px) { ... }
        'lg': '1024px',
        // => @media (min-width: 1024px) { ... }
        'xl': '1280px',
        // => @media (min-width: 1280px) { ... }
        '2xl': '1280px' // => @media (min-width: 1536px) { ... }

      },
      fontFamily: {
        sans: ["Poppins", "Segoe UI", "Helvetica Neue", "Arial", "sans-serif"]
      },
      fontSize: {
        // 'h1': '36px',
        // 'h2': '30px',
        // 'h3': '26px',
        // 'h4': '20px',
        // 'h5': '18px',
        // 'h6': '17px',
      },
      colors: {
        primary: {
          lighter: colors.blue['300'],
          "DEFAULT": colors.blue['800'],
          darker: ppnColor.blueDarker,
        },
        secondary: {
          lighter: colors.blue['100'],
          "DEFAULT": colors.blue['200'],
          darker: colors.blue['300']
        },
        background: {
          lighter: colors.blue['100'],
          "DEFAULT": colors.blue['200'],
          darker: colors.blue['300']
        },
        green: colors.emerald,
        yellow: colors.amber,
        cyellow: ppnColor.yellow,
        purple: colors.violet,
        cblue: '#48A1E2',
        corange: '#FF8800',
        cred: '#DF1414',
        ccgrey: '#F2F5F8',
        cgreen: '#3AC174',
        greenDarker: ppnColor.greenDarker,
        cgrey: {...ppnGray},
        
      },
      textColor: {
        orange: colors.orange,
        red: { ...colors.red,
          "DEFAULT": colors.red['500']
        },
        primary: {
          lighter: colors.gray['700'],
          "DEFAULT": colors.gray['800'],
          darker: ppnColor.blueDarker
        },
        secondary: {
          lighter: colors.gray['400'],
          "DEFAULT": colors.gray['600'],
          darker: colors.gray['800']
        }
      },
      backgroundColor: {
        primary: {
          lighter: ppnColor.blueLighter,
          "DEFAULT": ppnColor.blue,
          darker: ppnColor.blueDarker,
          50: '#48A1E2'
        },
        secondary: {
          lighter: colors.blue['100'],
          "DEFAULT": colors.blue['200'],
          darker: colors.blue['300']
        },
        container: {
          lighter: '#ffffff',
          "DEFAULT": '#ffffff',
          darker: '#000000'
        },
        cred: '#DF1414',
        cyellow: ppnColor.yellow,
        cgrey: {...ppnGray}
      },
      borderColor: {
        cgrey: {...ppnGray},
        primary: {
          lighter: ppnColor.blueLighter,
          "DEFAULT": ppnColor.blue,
          darker: ppnColor.blueDarker
        },
        secondary: {
          lighter: colors.blue['100'],
          "DEFAULT": colors.blue['200'],
          darker: colors.blue['300']
        },
        container: {
          lighter: '#ffffff',
          "DEFAULT": '#707070',
          darker: '#b6b6b6'
        }
      },
      minWidth: {
        8: spacing["8"],
        20: spacing["20"],
        40: spacing["40"],
        48: spacing["48"]
      },
      minHeight: {
        14: spacing["14"],
        a11y: '44px',
        'screen-25': '25vh',
        'screen-50': '50vh',
        'screen-75': '75vh'
      },
      maxHeight: {
        '0': '0',
        'screen-25': '25vh',
        'screen-50': '50vh',
        'screen-75': '75vh'
      },
      container: {
        center: true,
        padding: '.5rem'
      },
    }
  },
  plugins: [require('@tailwindcss/forms'), require('@tailwindcss/typography')],
  // Examples for excluding patterns from purge
  content: [
    // this theme's phtml and layout XML files
    '../../**/*.phtml',
    '../../*/layout/*.xml',
    '../../*/page_layout/override/base/*.xml',
    // parent theme in Vendor (if this is a child-theme)
    '../../../../../../../vendor/hyva-themes/magento2-default-theme/**/*.phtml',
    '../../../../../../../vendor/hyva-themes/magento2-default-theme/*/layout/*.xml',
    '../../../../../../../vendor/hyva-themes/magento2-default-theme/*/page_layout/override/base/*.xml',
    // app/code phtml files (if need tailwind classes from app/code modules)
    '../../../../../../../app/code/**/*.phtml',
  ]
});
