<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Checkout\Block\Cart\Sidebar as SidebarCart;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\SvgIcons;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var SvgIcons $hyvaicons */
$hyvaicons = $viewModels->require(SvgIcons::class);

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);
$showMiniCart = $storeConfig->getStoreConfig(SidebarCart::XML_PATH_CHECKOUT_SIDEBAR_DISPLAY);
?>
<script>
    function createAjaxLoader() {
        const loader = document.createElement('div');
        loader.className = 'absolute inset-0 flex justify-center items-center loader';

        return loader;
    }
    function addAjaxLoader(button, loader) {
        button.setAttribute('data-label', button.getAttribute('aria-label'));
        button.removeAttribute('aria-label');
        button.prepend(loader);
        button.classList.add('relative', '[&>:not(.loader)]:invisible');
        button.disabled = true;
    }
    function removeAjaxLoader(button, loader) {
        button.setAttribute('aria-label', button.getAttribute('data-label'));
        button.removeAttribute('data-label');
        loader.remove();
        button.classList.remove('[&>:not(.loader)]:invisible');
        button.disabled = false;
    }
    function initHeader () {
        return {
            searchOpen: false,
            cart: {},
            isCartOpen: false,
            isMobile: true,
            init() {
                this.checkIsSearchMobile();
            },
            getData(data) {
                const button = document.getElementById('menu-cart-icon');
                const loader = createAjaxLoader(button);
                if (data.cart) {
                    addAjaxLoader(button, loader);
                    this.cart = data.cart;
                    setTimeout(() => removeAjaxLoader(button, loader), 500);
                }
            },
            isCartEmpty() {
                return !this.cart.summary_count
            },
            checkIsSearchMobile() {
                const mobileElement = this.$refs.searchContainerMobile;
                this.isMobile = mobileElement
                    ? getComputedStyle(mobileElement).display !== "none"
                    : window.matchMedia('(max-width: 1023px)').matches; // Fallback to `md` breakpoint

                if (this.isMobile){
                    this.$refs.searchContainerMobile.appendChild(this.$refs.searchForm);
                    /* custom search hide in mobile */
                    this.$refs.searchForm.classList.add('search-hidden');
                } else {
                    this.$refs.searchContainerDesktop.appendChild(this.$refs.searchForm);
                }
            },
            toggleCart(event) {
                if (event.detail && event.detail.isOpen !== undefined) {
                    this.isCartOpen = event.detail.isOpen
                    if (!this.isCartOpen && this.$refs && this.$refs.cartButton) {
                        this.$refs.cartButton.focus()
                    }
                } else {
                    <?php
                    /*
                     * The toggle-cart event was previously dispatched without parameter to open the drawer (not toggle).
                     * Keeping this in here for backwards compatibility.
                     */
                    ?>
                    this.isCartOpen = true
                }
            },
            eventListeners: {
                ['@private-content-loaded.window'](event) {
                    this.getData(event.detail.data);
                },
                ['@visibilitychange.window.debounce']() {
                    this.checkIsSearchMobile();
                },
                ['@amx-close-search.window']() {
                    this.closeSearchForm();
                },
            },
            toggleSearchBar(element) {
                this.$refs.searchForm.classList.toggle('search-hidden');
                const searchInput = this.$refs.searchForm.querySelector('#search');
                searchInput && searchInput.focus();
            },
            closeSearchForm() {
                this.$refs.searchForm.classList.add('search-hidden');
            }
        }
    }
    function initCompareHeader() {
        return {
            compareProducts: null,
            itemCount: 0,
            receiveCompareData(data) {
                if (data['compare-products']) {
                    this.compareProducts = data['compare-products'];
                    this.itemCount = this.compareProducts.count;
                }
            }
        }
    }

    function initWishlistHeader() {
        return {
            wishlistItemsCount: 0,
            receiveWishListData(data) {
                if (data['wishlist']) {
                    let wishlist = data['wishlist'];
                    let counterText = wishlist.counter;
                    if (counterText) {
                        let counter = counterText.substring(0, counterText.indexOf(' '));
                        this.wishlistItemsCount = counter;
                    }

                }
            }
        }
    }
</script>
<div
    id="header"
    class="bg-white"
    x-data="initHeader()"
    x-bind="eventListeners"
>
    <?= $block->getChildHtml('header-notification') ?>

    <div class="container pt-3 flex gap-4 items-center justify-between">
        <div class="left-logo">
            <?= $block->getChildHtml('ninja.mobile.menu') ?>
            <?= $block->getChildHtml('logo'); ?>
        </div>

        <div x-ref="searchContainerDesktop" class="hidden lg:block min-h-a11y max-w-[430px] mx-auto px-6 flex-1"></div>

        <div class="flex gap-0.5 items-center">
            <div class="customer-type-container hidden lg:block">
                <div class="w-full mr-2">
                    <?= $block->getChildHtml('store-switcher'); ?>
                </div>
            </div>

            <!-- Mobile Search Icon only display purpose -->
            <a id="custom-mobile-only-search-link"
               class="relative inline-block no-underline ml-3 hover:text-black lg:hidden"
               href="javascript:void(0)"
               title="<?= $escaper->escapeHtml(__('Search Icon')) ?>"
               @click="toggleSearchBar()"
            >
                <?= $heroicons->searchHtml(
                    "size-5.5 md:size-7 hover:text-black",
                    25,
                    25
                ) ?>

                <span class="sr-only label">
                   <?= $escaper->escapeHtml(__('Search Icon')) ?>
                </span>
            </a>

            <a
                id="compare-link"
                class="relative inline-block rounded p-1 hover:bg-primary/10 outline-offset-2 hidden"
                :class="{ 'hidden': !(itemCount > 0) }"
                href="<?= $escaper->escapeUrl($block->getUrl('catalog/product_compare/index')) ?>"
                title="<?= $escaper->escapeHtmlAttr(__('Compare Products')) ?>"
                x-data="initCompareHeader()"
                @private-content-loaded.window="receiveCompareData($event.detail.data)"
                :aria-label="`
                    <?= $escaper->escapeHtmlAttr(__('Compare Products')) ?>,
                    ${itemCount > 1
                        ? hyva.str('<?= $escaper->escapeJs(__('%1 Items')) ?>', itemCount)
                        : hyva.str('<?= $escaper->escapeJs(__('%1 Item')) ?>', itemCount)
                    }`"
            >
                <?= $heroicons->scaleHtml('', 24, 24, ["aria-hidden" => "true"]) ?>
                <span
                    x-text="itemCount"
                    class="absolute min-w-5 h-5 -top-1.5 -right-1.5 px-0.5 py-0.5 rounded-full bg-primary-darker text-white
                        text-xs text-center"
                    aria-hidden="true"
                ></span>
            </a>

            <a
                id="wishlist-link"
                class="relative inline-block rounded p-1 hover:bg-primary/10 outline-offset-2 hidden"
                :class="{ 'hidden': !(wishlistItemsCount > 0) }"
                href="<?= $escaper->escapeUrl($block->getUrl('wishlist')) ?>"
                title="<?= $escaper->escapeHtmlAttr(__('Wishlist Products')) ?>"
                x-data="initWishlistHeader()"
                @private-content-loaded.window="receiveWishListData($event.detail.data)"
                :aria-label="`
                    <?= $escaper->escapeHtmlAttr(__('Wishlist Products')) ?>,
                    ${wishlistItemsCount > 1
                        ? hyva.str('<?= $escaper->escapeJs(__('%1 Items')) ?>', wishlistItemsCount)
                        : hyva.str('<?= $escaper->escapeJs(__('%1 Item')) ?>', wishlistItemsCount)
                    }`"
            >
                <?= $heroicons->heartHtml('', 24, 24, ["aria-hidden" => "true"]) ?>
                <span
                    x-text="wishlistItemsCount"
                    class="absolute min-w-5 min-h-5 -top-1.5 -right-1.5 px-0.5 py-0.5 rounded-full bg-primary-darker text-white
                        text-xs text-center"
                    aria-hidden="true"
                ></span>
            </a>

            <?= $block->getChildHtml('store-language-switcher'); ?>

            <?= $block->getChildHtml('customer') ?>

            <?php if ($showMiniCart): ?>
                <button
            <?php else: ?>
            <a
                <?php endif ?>
                id="menu-cart-icon"
                class="cart-btn"
                x-ref="cartButton"
                :aria-disabled="isCartEmpty()"
                :aria-label="`
                    <?= $escaper->escapeHtmlAttr($showMiniCart ? __('Toggle minicart') : __('View cart')) ?>,
                    ${isCartEmpty()
                        ? '<?= $escaper->escapeHtmlAttr(__('Cart is empty')) ?>'
                        : cart.summary_count > 1
                            ? hyva.str('<?= $escaper->escapeHtmlAttr(__('%1 items')) ?>', cart.summary_count)
                            : hyva.str('<?= $escaper->escapeHtmlAttr(__('%1 item')) ?>', cart.summary_count)
                    }`"
                <?php if ($showMiniCart): ?>
                    @click.prevent.stop="() => {
                        $dispatch('toggle-cart', { isOpen: true })
                    }"
                    @toggle-cart.window="toggleCart($event)"
                    :aria-expanded="isCartOpen"
                    aria-haspopup="dialog"
                <?php else: ?>
                    href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/index')) ?>"
                    title="<?= $escaper->escapeHtmlAttr(__('View cart')) ?>"
                <?php endif ?>
            >
                <?= $heroicons->shoppingCartHtml('', 24, 24, ["aria-hidden" => "true"]) ?>
                <span
                    x-text="cart.summary_count"
                    x-show="!isCartEmpty()"
                    x-cloak
                    class="absolute min-w-5 min-h-5 -top-1.5 -right-1.5 px-0.5 py-0.5 rounded-full bg-primary-darker text-white
                        text-xs text-center"
                    aria-hidden="true"
                ></span>
                <?php if ($showMiniCart): ?>
                    </button>
                <?php else: ?>
            </a>
        <?php endif ?>
        </div>
    </div>

    <div class="">
        <div x-ref="searchContainerMobile" class="lg:hidden">
            <div x-ref="searchForm" class="search-mbile-gap search-hidden">
                <?= $block->getChildHtml('header-search'); ?>
            </div>
        </div>
    </div>

    <?= $block->getChildHtml('header-usps'); ?>

    <?= $block->getChildHtml('cart-drawer'); ?>
    <?= $block->getChildHtml('authentication-popup'); ?>

    <!--Main Navigation-->
    <div class="ninja-main-menu py-1 lg:mt-2">
        <?= $block->getChildHtml('topmenu') ?>
    </div>
</div>
