<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Indexer/etc/indexer_merged.xsd">
    <indexer id="indexer_1" view_id="catalogsearch_fulltext" class="Magento\CatalogSearch\Model\Indexer\Fulltext">
        <title translate="true">Test Indexer Declaration 1</title>
        <description translate="true">Test Indexer Declaration 1</description>
        <fieldset name="default" source="Magento\Framework\Search\Index\ResourceModel" provider="Magento\Framework\Search\Index\Fieldset\Default">
            <field name="title" handler="Magento\Framework\Search\Index\Handler">
                <filter class="Magento\Framework\Search\Index\Filter\StopWordsFilter"/>
            </field>
            <field name="description" handler="default" xsi:type="searchable">
                <filter class="Magento\Framework\Search\Index\Filter\LowercaseFilter"/>
            </field>
            <field name="visibility" handler="default" xsi:type="filterable" dataType="int" />
        </fieldset>
        <fieldset name="related" source="Magento\Framework\Search\Index\ResourceModel" provider="Magento\Framework\Search\Index\Fieldset\Default">
            <reference fieldset="default" from="main_id" to="current_id" />
            <field name="description_2" handler="default" xsi:type="searchable">
                <filter class="Magento\Framework\Search\Index\Filter\LowercaseFilter"/>
            </field>
        </fieldset>
        <structure class="Magento\Framework\Search\Index\Structure"/>
        <saveHandler class="Magento\Framework\Search\Index\SaveHandler"/>
    </indexer>
    <indexer id="indexer_2" view_id="catalogsearch_fulltext_2" class="Magento\CatalogSearch\Model\Indexer\Fulltext">
        <title translate="true">Test Indexer Declaration 1</title>
        <description translate="true">Test Indexer Declaration 1</description>
        <fieldset name="default" source="Magento\Framework\Search\Index\ResourceModel" provider="Magento\Framework\Search\Index\Fieldset\Default">
            <field name="title" handler="Magento\Framework\Search\Index\Handler">
                <filter class="Magento\Framework\Search\Index\Filter\StopWordsFilter"/>
            </field>
            <field name="description" handler="default" xsi:type="searchable">
                <filter class="Magento\Framework\Search\Index\Filter\LowercaseFilter"/>
            </field>
            <field name="visibility" handler="default" xsi:type="filterable" dataType="int" />
        </fieldset>
        <structure class="Magento\Framework\Search\Index\Structure"/>
        <saveHandler class="Magento\Framework\Search\Index\SaveHandler"/>
    </indexer>
</config>
