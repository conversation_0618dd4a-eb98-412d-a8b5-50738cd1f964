<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="test_table" resource="default" comment="Test Table">
        <column xsi:type="int" name="page_id" nullable="false" unsigned="false" identity="true"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="page_id"/>
        </constraint>
    </table>
    <table name="test_scope_table" resource="default" comment="Test Scope Table">
        <column xsi:type="int" name="scope_id" nullable="false" unsigned="false" identity="true"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="scope_id"/>
        </constraint>
    </table>
    <table name="dependent" resource="default" comment="Lol">
        <column xsi:type="int" name="page_id_on" nullable="false" unsigned="false"/>
        <column xsi:type="int" name="scope_id_on" nullable="false" unsigned="false"/>
        <!-- Expected name in DB: DEPENDENT_PAGE_ID_ON_TEST_TABLE_PAGE_ID -->
        <constraint xsi:type="foreign" referenceId="DEPENDENT_PAGE_ID_ON_TEST_TABLE_PAGE_ID" table="dependent" column="page_id_on" referenceColumn="page_id"
                    referenceTable="test_table"/>
        <!-- Expected name in DB: DEPENDENT_SCOPE_ID_ON_TEST_SCOPE_TABLE_SCOPE_ID -->
        <constraint xsi:type="foreign" referenceId="ScopeIDOnTOScopeTableAndScopeId" table="dependent" column="scope_id_on" referenceColumn="scope_id"
                    referenceTable="test_scope_table"/>
    </table>
</schema>
