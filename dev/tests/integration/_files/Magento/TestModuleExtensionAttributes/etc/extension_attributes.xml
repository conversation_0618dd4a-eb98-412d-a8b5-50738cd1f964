<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    <extension_attributes for="Magento\Catalog\Api\Data\ProductInterface">
        <attribute code="test_stock_item" type="Magento\CatalogInventory\Api\Data\StockItemInterface">
            <resources>
                <resource ref="Magento_CatalogInventory::cataloginventory"/>
            </resources>
            <join reference_table="cataloginventory_stock_item"
                  join_on_field="entity_id"
                  reference_field="product_id"
                    >
                <field>qty</field>
                <field>item_id</field>
            </join>
        </attribute>
        <attribute code="test_stock_item_qty" type="string">
            <resources>
                <resource ref="Magento_CatalogInventory::cataloginventory"/>
            </resources>
            <join reference_table="cataloginventory_stock_item"
                  join_on_field="entity_id"
                  reference_field="product_id"
                    >
                <field>qty</field>
            </join>
        </attribute>
    </extension_attributes>

    <extension_attributes for="Magento\TestModuleExtensionAttributes\Api\Data\FakeCustomerInterface">
        <attribute code="test_group_code" type="string">
            <join reference_table="customer_group"
                  join_on_field="group_id"
                  reference_field="customer_group_id"
                    >
                <field>customer_group_code</field>
            </join>
        </attribute>
    </extension_attributes>

    <extension_attributes for="Magento\Customer\Api\Data\CustomerInterface">
        <attribute code="test_group_code" type="string">
            <join reference_table="customer_group"
                  join_on_field="group_id"
                  reference_field="customer_group_id"
                    >
                <field>customer_group_code</field>
            </join>
        </attribute>
        <attribute code="test_group" type="Magento\Customer\Api\Data\GroupInterface">
            <join reference_table="customer_group"
                  join_on_field="group_id"
                  reference_field="customer_group_id"
                    >
                <field>tax_class_id</field>
                <field column="customer_group_code">code</field>
            </join>
        </attribute>
    </extension_attributes>

    <extension_attributes for="Magento\Customer\Api\Data\GroupInterface">
        <attribute code="test_dummy_attribute" type="string">
            <join reference_table="eav_attribute"
                  reference_field="attribute_id"
                  join_on_field="customer_group_id"
                    >
                <field>attribute_code</field>
            </join>
        </attribute>
        <attribute code="test_complex_dummy_attribute" type="Magento\Catalog\Api\Data\ProductAttributeInterface">
            <join reference_table="admin_user"
                  reference_field="user_id"
                  join_on_field="customer_group_id"
                    >
                <field column="username">attribute_code</field>
                <field column="firstname">frontend_label</field>
            </join>
        </attribute>
    </extension_attributes>

    <extension_attributes for="Magento\Sales\Api\Data\InvoiceInterface">
        <attribute code="test_dummy_attribute" type="string">
            <join reference_table="eav_attribute"
                  reference_field="attribute_id"
                  join_on_field="entity_id"
                    >
                <field>attribute_code</field>
            </join>
        </attribute>
    </extension_attributes>
</config>
