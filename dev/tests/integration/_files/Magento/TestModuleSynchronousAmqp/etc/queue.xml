<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/queue.xsd">
    <broker topic="synchronous.rpc.test.deprecated" type="amqp" exchange="magento">
        <queue consumer="synchronousRpcTestConsumer.deprecated" name="synchronous.rpc.test.deprecated" consumerInstance="Magento\Framework\MessageQueue\Rpc\Consumer"/>
    </broker>
</config>
