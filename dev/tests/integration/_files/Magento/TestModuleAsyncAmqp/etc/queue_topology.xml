<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/topology.xsd">
    <exchange name="magento" connection="amqp" type="topic">
        <binding id="async.binding1" topic="multi.topic.queue.topic.c" destinationType="queue" destination="queue.for.multiple.topics.test.c"/>
        <binding id="async.binding2" topic="multi.topic.queue.topic.c" destinationType="queue" destination="mixed.sync.and.async.queue"/>
        <binding id="async.binding3" topic="multi.topic.queue.topic.d" destinationType="queue" destination="queue.for.multiple.topics.test.d"/>
        <binding id="async.binding4" topic="mtmh.topic.1" destinationType="queue" destination="mtmh.queue.1"/>
        <binding id="async.binding5" topic="mtmh.topic.1" destinationType="queue" destination="mtmh.queue.2"/>
        <binding id="async.binding6" topic="mtmh.topic.2" destinationType="queue" destination="mtmh.queue.1"/>
        <binding id="async.binding7" topic="mtmh.topic.2" destinationType="queue" destination="mtmh.queue.2"/>
        <binding id="async.binding8" topic="multi.topic.queue.topic.y" destinationType="queue" destination="queue.for.multiple.topics.test.y"/>
        <binding id="async.binding9" topic="multi.topic.queue.topic.z" destinationType="queue" destination="queue.for.multiple.topics.test.z"/>

        <binding id="wildCardBinding1" topic="#.wildcard" destinationType="queue" destination="wildcard.queue.one"/>
        <binding id="wildCardBinding2" topic="*.*.segment3.wildcard" destinationType="queue" destination="wildcard.queue.two"/>
        <binding id="wildCardBinding3" topic="*.segment3.wildcard" destinationType="queue" destination="wildcard.queue.three"/>
        <binding id="wildCardBinding4" topic="#.segment3.*" destinationType="queue" destination="wildcard.queue.four"/>
    </exchange>
</config>




