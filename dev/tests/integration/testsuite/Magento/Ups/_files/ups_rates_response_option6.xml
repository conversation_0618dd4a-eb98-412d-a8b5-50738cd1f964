<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<RatingServiceSelectionResponse>
    <Response>
        <TransactionReference>
            <CustomerContext>Rating and Service</CustomerContext>
            <XpciVersion>1.0</XpciVersion>
        </TransactionReference>
        <ResponseStatusCode>1</ResponseStatusCode>
        <ResponseStatusDescription>Success</ResponseStatusDescription>
    </Response>
    <RatedShipment>
        <Service>
            <Code>07</Code>
        </Service>
        <RatedShipmentWarning>Your invoice may vary from the displayed reference
            rates
        </RatedShipmentWarning>
        <BillingWeight>
            <UnitOfMeasurement>
                <Code>KGS</Code>
            </UnitOfMeasurement>
            <Weight>2.0</Weight>
        </BillingWeight>
        <TransportationCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>35.16</MonetaryValue>
        </TransportationCharges>
        <ServiceOptionsCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>0.00</MonetaryValue>
        </ServiceOptionsCharges>
        <TotalCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>35.16</MonetaryValue>
        </TotalCharges>
        <GuaranteedDaysToDelivery>1</GuaranteedDaysToDelivery>
        <ScheduledDeliveryTime>10:30 A.M.</ScheduledDeliveryTime>
        <RatedPackage>
            <TransportationCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </TransportationCharges>
            <ServiceOptionsCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </ServiceOptionsCharges>
            <TotalCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </TotalCharges>
            <Weight>2.0</Weight>
            <BillingWeight>
                <UnitOfMeasurement>
                    <Code/>
                </UnitOfMeasurement>
                <Weight/>
            </BillingWeight>
        </RatedPackage>
        <NegotiatedRates>
            <NetSummaryCharges>
                <GrandTotal>
                    <CurrencyCode>GBP</CurrencyCode>
                    <MonetaryValue>44.37</MonetaryValue>
                </GrandTotal>
            </NetSummaryCharges>
        </NegotiatedRates>
    </RatedShipment>
    <RatedShipment>
        <Service>
            <Code>08</Code>
        </Service>
        <RatedShipmentWarning>Your invoice may vary from the displayed reference
            rates
        </RatedShipmentWarning>
        <BillingWeight>
            <UnitOfMeasurement>
                <Code>KGS</Code>
            </UnitOfMeasurement>
            <Weight>2.0</Weight>
        </BillingWeight>
        <TransportationCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>34.15</MonetaryValue>
        </TransportationCharges>
        <ServiceOptionsCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>0.00</MonetaryValue>
        </ServiceOptionsCharges>
        <TotalCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>34.15</MonetaryValue>
        </TotalCharges>
        <GuaranteedDaysToDelivery/>
        <ScheduledDeliveryTime/>
        <RatedPackage>
            <TransportationCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </TransportationCharges>
            <ServiceOptionsCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </ServiceOptionsCharges>
            <TotalCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </TotalCharges>
            <Weight>2.0</Weight>
            <BillingWeight>
                <UnitOfMeasurement>
                    <Code/>
                </UnitOfMeasurement>
                <Weight/>
            </BillingWeight>
        </RatedPackage>
        <NegotiatedRates>
            <NetSummaryCharges>
                <GrandTotal>
                    <CurrencyCode>GBP</CurrencyCode>
                    <MonetaryValue>60.57</MonetaryValue>
                </GrandTotal>
            </NetSummaryCharges>
        </NegotiatedRates>
    </RatedShipment>
    <RatedShipment>
        <Service>
            <Code>65</Code>
        </Service>
        <RatedShipmentWarning>Your invoice may vary from the displayed reference
            rates
        </RatedShipmentWarning>
        <BillingWeight>
            <UnitOfMeasurement>
                <Code>KGS</Code>
            </UnitOfMeasurement>
            <Weight>2.0</Weight>
        </BillingWeight>
        <TransportationCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>29.59</MonetaryValue>
        </TransportationCharges>
        <ServiceOptionsCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>0.00</MonetaryValue>
        </ServiceOptionsCharges>
        <TotalCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>29.59</MonetaryValue>
        </TotalCharges>
        <GuaranteedDaysToDelivery>1</GuaranteedDaysToDelivery>
        <ScheduledDeliveryTime/>
        <RatedPackage>
            <TransportationCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </TransportationCharges>
            <ServiceOptionsCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </ServiceOptionsCharges>
            <TotalCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </TotalCharges>
            <Weight>2.0</Weight>
            <BillingWeight>
                <UnitOfMeasurement>
                    <Code/>
                </UnitOfMeasurement>
                <Weight/>
            </BillingWeight>
        </RatedPackage>
        <NegotiatedRates>
            <NetSummaryCharges>
                <GrandTotal>
                    <CurrencyCode>GBP</CurrencyCode>
                    <MonetaryValue>41.61</MonetaryValue>
                </GrandTotal>
            </NetSummaryCharges>
        </NegotiatedRates>
    </RatedShipment>
    <RatedShipment>
        <Service>
            <Code>54</Code>
        </Service>
        <RatedShipmentWarning>Your invoice may vary from the displayed reference
            rates
        </RatedShipmentWarning>
        <BillingWeight>
            <UnitOfMeasurement>
                <Code>KGS</Code>
            </UnitOfMeasurement>
            <Weight>2.0</Weight>
        </BillingWeight>
        <TransportationCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>45.18</MonetaryValue>
        </TransportationCharges>
        <ServiceOptionsCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>0.00</MonetaryValue>
        </ServiceOptionsCharges>
        <TotalCharges>
            <CurrencyCode>GBP</CurrencyCode>
            <MonetaryValue>45.18</MonetaryValue>
        </TotalCharges>
        <GuaranteedDaysToDelivery>1</GuaranteedDaysToDelivery>
        <ScheduledDeliveryTime>8:30 A.M.</ScheduledDeliveryTime>
        <RatedPackage>
            <TransportationCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </TransportationCharges>
            <ServiceOptionsCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </ServiceOptionsCharges>
            <TotalCharges>
                <CurrencyCode/>
                <MonetaryValue/>
            </TotalCharges>
            <Weight>2.0</Weight>
            <BillingWeight>
                <UnitOfMeasurement>
                    <Code/>
                </UnitOfMeasurement>
                <Weight/>
            </BillingWeight>
        </RatedPackage>
        <NegotiatedRates>
            <NetSummaryCharges>
                <GrandTotal>
                    <CurrencyCode>GBP</CurrencyCode>
                    <MonetaryValue>157.47</MonetaryValue>
                </GrandTotal>
            </NetSummaryCharges>
        </NegotiatedRates>
    </RatedShipment>
</RatingServiceSelectionResponse>
