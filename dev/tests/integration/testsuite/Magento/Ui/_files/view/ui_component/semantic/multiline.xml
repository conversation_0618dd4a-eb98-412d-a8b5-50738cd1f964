<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<multiline xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
           name="string"
           template="string"
           component="string"
           class="string"
           provider="string"
           sortOrder="0"
           displayArea="string">
    <settings>

        <!-- uiElementSettings -->
        <statefull>
            <property xsi:type="boolean" name="anySimpleType">true</property>
        </statefull>
        <imports>
            <link active="false" name="string">string</link>
        </imports>
        <exports>
            <link active="false" name="string">string</link>
        </exports>
        <links>
            <link active="false" name="string">string</link>
        </links>
        <listens>
            <link active="false" name="string">string</link>
        </listens>
        <deps>
            <dep active="false">string</dep>
        </deps>
        <ns>string</ns>
        <componentType>string</componentType>
        <dataScope>string</dataScope>
        <storageConfig>
            <provider>string</provider>
            <namespace>string</namespace>
            <path path="string">
                <param name="string">string</param>
            </path>
        </storageConfig>
        <!-- /uiElementSettings -->

        <!-- uiCollectionSettings -->
        <childDefaults>
            <param xsi:type="boolean" active="false" name="anySimpleType">
                false
            </param>
        </childDefaults>
        <!-- /uiCollectionSettings -->

        <label translate="false">string</label>
        <visible>false</visible>
        <additionalClasses>
            <class name="string">false</class>
        </additionalClasses>
        <showLabel>false</showLabel>
        <breakLine>false</breakLine>
        <fieldTemplate>string</fieldTemplate>
        <validateWholeGroup>false</validateWholeGroup>
        <required>false</required>

    </settings>
</multiline>
