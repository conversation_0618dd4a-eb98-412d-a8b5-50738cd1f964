<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<exportButton xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
              name="string"
              template="string"
              component="string"
              class="string"
              provider="string"
              sortOrder="0"
              displayArea="string">
    <settings>

        <!-- uiElementSettings -->
        <statefull>
            <property xsi:type="boolean" name="anySimpleType">true</property>
        </statefull>
        <imports>
            <link active="false" name="string">string</link>
        </imports>
        <exports>
            <link active="false" name="string">string</link>
        </exports>
        <links>
            <link active="false" name="string">string</link>
        </links>
        <listens>
            <link active="false" name="string">string</link>
        </listens>
        <deps>
            <dep active="false">string</dep>
        </deps>
        <ns>string</ns>
        <componentType>string</componentType>
        <dataScope>string</dataScope>
        <storageConfig>
            <provider>string</provider>
            <namespace>string</namespace>
            <path path="string">
                <param name="string">string</param>
            </path>
        </storageConfig>
        <!-- /uiElementSettings -->

        <selectProvider>string</selectProvider>
        <options>
            <option xsi:type="string" active="false" name="anySimpleType">string</option>
        </options>
        <additionalParams>
            <param xsi:type="string" active="false" name="anySimpleType">
                string
            </param>
        </additionalParams>

    </settings>
</exportButton>
