<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details. 
 */
-->
<textarea xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="config" xsi:type="array">
            <item name="cols" xsi:type="number">0</item>
            <item name="rows" xsi:type="number">0</item>
            <item name="visible" xsi:type="boolean">false</item>
            <item name="disabled" xsi:type="boolean">false</item>
            <item name="labelVisible" xsi:type="boolean">false</item>
            <item name="showFallbackReset" xsi:type="boolean">false</item>
            <item name="focused" xsi:type="boolean">false</item>
            <item name="label" xsi:type="string" translate="true">string</item>
            <item name="dataType" xsi:type="string">string</item>
            <item name="elementTmpl" xsi:type="string">string</item>
            <item name="tooltipTpl" xsi:type="string">string</item>
            <item name="fallbackResetTpl" xsi:type="string">string</item>
            <item name="placeholder" xsi:type="string" translate="true">text</item>
            <item name="validation" xsi:type="array">
                <item name="anySimpleType" active="false" xsi:type="boolean">true</item>
            </item>
            <item name="notice" xsi:type="string" translate="true">string</item>
            <item name="required" xsi:type="boolean">false</item>
            <item name="switcherConfig" xsi:type="array">
                <item name="name" xsi:type="string">string</item>
                <item name="component" xsi:type="string">string</item>
                <item name="target" xsi:type="string">string</item>
                <item name="property" xsi:type="string">string</item>
                <item name="enabled" xsi:type="boolean">true</item>
                <item name="rules" xsi:type="array">
                    <item name="string" xsi:type="array">
                        <item name="value" xsi:type="string">string</item>
                        <item name="actions" xsi:type="array">
                            <item name="string" xsi:type="array">
                                <item name="target" xsi:type="string">string</item>
                                <item name="callback" xsi:type="string">string</item>
                                <item name="params" xsi:type="array">
                                    <item name="string" active="true" xsi:type="string"/>
                                </item>
                            </item>
                        </item>
                    </item>
                </item>
            </item>
            <item name="tooltip" xsi:type="array">
                <item name="link" xsi:type="string">string</item>
                <item name="description" xsi:type="string" translate="true">string</item>
            </item>
            <item name="additionalClasses" xsi:type="array">
                <item name="string" xsi:type="boolean">false</item>
            </item>
            <item name="addbefore" xsi:type="string" translate="true">string</item>
            <item name="addafter" xsi:type="string" translate="true">string</item>
            <item name="provider" xsi:type="string">string</item>
            <item name="component" xsi:type="string">string</item>
            <item name="template" xsi:type="string">string</item>
            <item name="sortOrder" xsi:type="number">0</item>
            <item name="displayArea" xsi:type="string">string</item>
            <item name="storageConfig" xsi:type="array">
                <item name="provider" xsi:type="string">string</item>
                <item name="namespace" xsi:type="string">string</item>
                <item name="path" xsi:type="url" path="string">
                    <param name="string">string</param>
                </item>
            </item>
            <item name="statefull" xsi:type="array">
                <item name="anySimpleType" xsi:type="boolean">true</item>
            </item>
            <item name="imports" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="exports" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="links" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="listens" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="ns" xsi:type="string">string</item>
            <item name="componentType" xsi:type="string">string</item>
            <item name="dataScope" xsi:type="string">string</item>
        </item>
        <item name="js_config" xsi:type="array">
            <item name="deps" xsi:type="array">
                <item name="0" xsi:type="string">string</item>
            </item>
        </item>
    </argument>
</textarea>
