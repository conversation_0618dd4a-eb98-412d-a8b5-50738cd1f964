<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details. 
 */
-->
<actionsColumn xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="config" xsi:type="array">
            <item name="templates" xsi:type="array">
                <item name="actions" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="label" xsi:type="string" translate="false"/>
                        <item name="callback" xsi:type="array">
                            <item name="params" xsi:type="array">
                                <item name="string" xsi:type="string">string</item>
                            </item>
                            <item name="provider" xsi:type="string">string</item>
                            <item name="target" xsi:type="string">string</item>
                        </item>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">string</item>
                            <item name="message" xsi:type="string" translate="true">string</item>
                            <item name="string" xsi:type="string">string</item>
                        </item>
                        <item name="index" xsi:type="string">0</item>
                        <item name="href" xsi:type="string">string</item>
                    </item>
                </item>
            </item>
            <item name="indexField" xsi:type="string">string</item>
            <item name="draggable" xsi:type="boolean">false</item>
            <item name="sorting" xsi:type="string">asc</item>
            <item name="sortable" xsi:type="boolean">false</item>
            <item name="controlVisibility" xsi:type="boolean">false</item>
            <item name="bodyTmpl" xsi:type="string">string</item>
            <item name="headerTmpl" xsi:type="string">string</item>
            <item name="label" xsi:type="string" translate="true"/>
            <item name="fieldClass" xsi:type="array">
                <item name="string" xsi:type="boolean">false</item>
            </item>
            <item name="disableAction" xsi:type="boolean">false</item>
            <item name="filter" xsi:type="string">true</item>
            <item name="dataType" xsi:type="string">string</item>
            <item name="visible" xsi:type="boolean">false</item>
            <item name="resizeEnabled" xsi:type="boolean">false</item>
            <item name="add_field" xsi:type="boolean">false</item>
            <item name="has_preview" xsi:type="boolean">false</item>
            <item name="altField" xsi:type="string">string</item>
            <item name="resizeDefaultWidth" xsi:type="number">0</item>
            <item name="fieldAction" xsi:type="array">
                <item name="provider" xsi:type="string">string</item>
                <item name="target" xsi:type="string">string</item>
                <item name="params" xsi:type="array">
                    <item name="0" xsi:type="boolean">true</item>
                </item>
            </item>
            <item name="dateFormat" xsi:type="string">string</item>
            <item name="timeFormat" xsi:type="string">string</item>
            <item name="timezone" xsi:type="string">false</item>
            <item name="provider" xsi:type="string">string</item>
            <item name="component" xsi:type="string">string</item>
            <item name="template" xsi:type="string">string</item>
            <item name="sortOrder" xsi:type="number">0</item>
            <item name="displayArea" xsi:type="string">string</item>
            <item name="storageConfig" xsi:type="array">
                <item name="provider" xsi:type="string">string</item>
                <item name="namespace" xsi:type="string">string</item>
                <item name="path" xsi:type="url" path="string">
                    <param name="string">string</param>
                </item>
            </item>
            <item name="statefull" xsi:type="array">
                <item name="anySimpleType" xsi:type="boolean">true</item>
            </item>
            <item name="imports" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="exports" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="links" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="listens" xsi:type="array">
                <item name="string" xsi:type="string">string</item>
            </item>
            <item name="ns" xsi:type="string">string</item>
            <item name="componentType" xsi:type="string">string</item>
            <item name="dataScope" xsi:type="string">string</item>
        </item>
        <item name="options" xsi:type="array">
            <item name="anySimpleType" xsi:type="boolean">true</item>
        </item>
        <item name="js_config" xsi:type="array">
            <item name="deps" xsi:type="array">
                <item name="0" xsi:type="string">string</item>
            </item>
        </item>
    </argument>
</actionsColumn>
