<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Integration:etc/integration/integration.xsd">
    <integration name="TestIntegration1">
        <email><EMAIL></email>
        <endpoint_url>http://example.com/endpoint1</endpoint_url>
        <identity_link_url>http://www.example.com/identity1</identity_link_url>
        <resources>
            <resource name="Magento_Customer::manage" />
            <resource name="Magento_Sales::capture" />
            <resource name="Magento_SalesRule::quote" />
        </resources>
    </integration>
    <integration name="TestIntegration2">
        <email><EMAIL></email>
        <resources>
            <resource name="Magento_Sales::capture" />
        </resources>
    </integration>
    <integration name="TestIntegration3">
        <email><EMAIL></email>
        <resources>
            <resource name="Magento_Sales::create" />
            <resource name="Magento_SalesRule::quote" />
        </resources>
    </integration>
</config>
