type Query {
    products(
    search: String,
    filter: ProductFilterInput,
    pageSize: Int =10 @doc(description: "number of records to display") ,
    currentPage: Int = 1,
    sort: ProductSortInput
    ): Products
    @resolver(class: "Magento\\CatalogGraphQl\\Model\\Resolver\\Products")
    @doc(description: "comment for products fields")
}

enum CurrencyEnum {
    AFN
    GBP
    EUR
    INR
    USD
}

type Price {
    amount: Money
    adjustments: [PriceAdjustment]
}

type PriceAdjustment {
    amount: Money
    code: PriceAdjustmentCodesEnum
    description: PriceAdjustmentDescriptionEnum
}

enum PriceAdjustmentCodesEnum {
    TAX
}

enum PriceAdjustmentDescriptionEnum @doc(description: "Description for enumType PriceAdjustmentDescriptionEnum") {
    INCLUDED @doc(description: "price is included")
    EXCLUDED @doc(description: "price is excluded")
}

enum PriceTypeEnum {
    FIXED
    PERCENT
    DYNAMIC
}

type Money {
    value: Float
    currency: CurrencyEnum
}

type ProductPrices {
    minimalPrice: Price
    maximalPrice: Price
    regularPrice: Price
}

type ProductCategoryLinks {
    position: Int
    category_id: String
}


type ProductLinks implements ProductLinksInterface @doc(description: "ProductLinks is an implementation of ProductLinksInterface.") {

}

interface ProductLinksInterface @typeResolver(class: "Magento\\CatalogGraphQl\\Model\\ProductLinkTypeResolverComposite") @doc(description: "description for ProductLinksInterface") {
    sku: String @doc(description:"The identifier of the linked product")
    link_type: String
    linked_product_sku: String @doc(description:"The SKU of the linked product")
    linked_product_type: String
    position: Int @doc(description:"The position within the list of product links")
}

type ProductTierPrices {
    customer_group_id: String
    qty: Float
    value: Float
    percentage_value: Float
    website_id: Float
}

interface ProductInterface
@typeResolver(class: "Magento\\CatalogGraphQl\\Model\\ProductInterfaceTypeResolverComposite")
@doc(description: "comment for ProductInterface")
 {
    id: Int @doc(description: "comment for [ProductInterface].")
    name: String
    sku: String
    special_price: Float
    special_from_date: String
    attribute_set_id: Int
    tier_price: Float
    category_ids: [Int]
    updated_at: String
    country_of_manufacture: String
    type_id: String
    website_ids: [Int]
    category_links: [ProductCategoryLinks]
    product_links: [ProductLinksInterface]
    media_gallery_entries: [MediaGalleryEntry]
    tier_prices: [ProductTierPrices]
    price: ProductPrices
    manufacturer: Int
}

interface PhysicalProductInterface @typeResolver(class: "Magento\\CatalogGraphQl\\Model\\ProductInterfaceTypeResolverComposite") @doc(description:"Comment for empty PhysicalProductInterface") {

}


type CustomizableAreaOption implements CustomizableOptionInterface {
    value: CustomizableAreaValue
    product_sku: String
    title: String @doc(description:"Comment for title field for CustomizableAreaOption concrete type")
    required: Boolean
    sort_order: Int
}

type CustomizableAreaValue {
    price: Float
    price_type: PriceTypeEnum
    sku: String
    max_characters: Int
}

type CustomizableDateOption implements CustomizableOptionInterface {
    value: CustomizableDateValue
    product_sku: String
    title: String @doc(description:"This description should override interface comment.")
    required: Boolean
    sort_order: Int
}

type CustomizableDateValue {
    price: Float
    price_type: PriceTypeEnum
    sku: String
}

type CustomizableDropDownOption implements CustomizableOptionInterface {
    value: [CustomizableDropDownValue]
    required: Boolean @doc(description:"Comment for required field for CustomizableDropDownOption concrete type")
}

type CustomizableDropDownValue {
    option_type_id: Int
    price: Float
    price_type: PriceTypeEnum
    sku: String
    title: String
    sort_order: Int
}

type CustomizableFieldOption implements CustomizableOptionInterface {
    value: CustomizableFieldValue
    product_sku: String
    sort_order: Int @doc(description:"Comment for sort_order for CustomizableFieldOption concrete type")
}

type CustomizableFieldValue {
    price: Float
    price_type: PriceTypeEnum
    sku: String
    max_characters: Int
}

type CustomizableFileOption implements CustomizableOptionInterface {
    value: CustomizableFileValue
    product_sku: String
}

type CustomizableFileValue {
    price: Float
    price_type: PriceTypeEnum
    sku: String
    file_extension: String
    image_size_x: Int
    image_size_y: Int
}

interface CustomizableOptionInterface @typeResolver(class: "Magento\\CatalogGraphQl\\Model\\CustomizableOptionTypeResolver") {
    title: String @doc(description:"Comment for CustomizableOptionInterface")
    required: Boolean
    sort_order: Int
}

interface CustomizableProductInterface @typeResolver(class: "Magento\\CatalogGraphQl\\Model\\ProductInterfaceTypeResolverComposite") {
    options: [CustomizableOptionInterface]
}

type CustomizableRadioOption implements CustomizableOptionInterface {
    value: [CustomizableRadioValue]
}

type CustomizableRadioValue {
    option_type_id: Int
    price: Float
    price_type: PriceTypeEnum
    sku: String
    title: String
    sort_order: Int
}

type VirtualProduct implements ProductInterface, CustomizableProductInterface {
}

type SimpleProduct implements ProductInterface, PhysicalProductInterface, CustomizableProductInterface
@doc(description: "Comment for empty SimpleProduct type")
{
}

type Products @doc(description:"Comment for Products") {
    items: [ProductInterface] @doc(description: "comment for items[Products].")
    page_info: SearchResultPageInfo @doc(description: "comment for page_info.")
    total_count: Int
}

input ProductFilterInput @doc(description:"Comment for ProductFilterInput") {
    name: FilterTypeInput
    sku: FilterTypeInput @doc(description:"Comment for field_sku which is of type FilterTypeInput")
    description: FilterTypeInput
    short_description: FilterTypeInput
    price: FilterTypeInput
    special_price: FilterTypeInput
    special_from_date: FilterTypeInput
    special_to_date: FilterTypeInput
    weight: FilterTypeInput
    manufacturer: FilterTypeInput
    meta_title: FilterTypeInput
    meta_keyword: FilterTypeInput
    meta_description: FilterTypeInput
    image: FilterTypeInput
    small_image: FilterTypeInput
    thumbnail: FilterTypeInput
    tier_price: FilterTypeInput
    news_from_date: FilterTypeInput
    news_to_date: FilterTypeInput
    custom_design: FilterTypeInput
    custom_design_from: FilterTypeInput
    custom_design_to: FilterTypeInput
    custom_layout_update: FilterTypeInput
    page_layout: FilterTypeInput
    category_ids: FilterTypeInput
    options_container: FilterTypeInput
    required_options: FilterTypeInput
    has_options: FilterTypeInput
    image_label: FilterTypeInput
    small_image_label: FilterTypeInput
    thumbnail_label: FilterTypeInput
    created_at: FilterTypeInput
    updated_at: FilterTypeInput
    country_of_manufacture: FilterTypeInput
    custom_layout: FilterTypeInput
    gift_message_available: FilterTypeInput
    or: ProductFilterInput
}

type ProductMediaGalleryEntriesContent @doc(description: "Comment for ProductMediaGalleryEntriesContent.") {
    base64_encoded_data: String
    type: String
    name: String
}

type ProductMediaGalleryEntriesVideoContent {
    media_type: String
    video_provider: String
    video_url: String
    video_title: String
    video_description: String
    video_metadata: String
}

input ProductSortInput @doc(description:"Input ProductSortInput") {
    name: SortEnum @doc(description:"Name")
    sku: SortEnum
    description: SortEnum
    short_description: SortEnum
    price: SortEnum
    special_price: SortEnum
    special_from_date: SortEnum
    special_to_date: SortEnum
    weight: SortEnum
    manufacturer: SortEnum
    meta_title: SortEnum
    meta_keyword: SortEnum
    meta_description: SortEnum
    image: SortEnum
    small_image: SortEnum
    thumbnail: SortEnum
    tier_price: SortEnum
    news_from_date: SortEnum
    news_to_date: SortEnum
    custom_design: SortEnum
    custom_design_from: SortEnum
    custom_design_to: SortEnum
    custom_layout_update: SortEnum
    page_layout: SortEnum
    category_ids: SortEnum
    options_container: SortEnum
    required_options: SortEnum
    has_options: SortEnum
    image_label: SortEnum
    small_image_label: SortEnum
    thumbnail_label: SortEnum
    created_at: SortEnum
    updated_at: SortEnum
    country_of_manufacture: SortEnum
    custom_layout: SortEnum
    gift_message_available: SortEnum
}

type MediaGalleryEntry
@doc(description: "Comment for MediaGalleryEntry type")
{
    id: Int @doc(description: "id for MediaGalleryEntry")
    media_type: String
    label: String
    position: Int
    disabled: Boolean
    types: [String]
    file: String
    content: ProductMediaGalleryEntriesContent @doc(description: "Comment for ProductMediaGalleryEntriesContent on content field")
    video_content: ProductMediaGalleryEntriesVideoContent
}

type EntityUrl {
    id: Int
    canonical_url: String
    type: UrlRewriteEntityTypeEnum
}
enum UrlRewriteEntityTypeEnum @doc(description: "Comment for empty Enum")
{

}


