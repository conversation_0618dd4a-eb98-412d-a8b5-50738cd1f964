<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Magento\CatalogUrlRewrite\Model\Product;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\CatalogUrlRewrite\Model\ObjectRegistryFactory;
use Magento\Framework\ObjectManagerInterface;
use Magento\Store\Model\Store;
use Magento\TestFramework\Helper\Bootstrap;
use PHPUnit\Framework\TestCase;

/**
 * Verify generate url rewrites for anchor categories.
 */
class AnchorUrlRewriteGeneratorTest extends TestCase
{

    /**
     * @var ObjectManagerInterface
     */
    private $objectManager;

    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @var ObjectRegistryFactory
     */
    private $objectRegistryFactory;

    /**
     * @inheritDoc
     */
    protected function setUp(): void
    {
        parent::setUp(); // TODO: Change the autogenerated stub

        $this->objectManager = Bootstrap::getObjectManager();
        $this->productRepository = $this->objectManager->create(ProductRepositoryInterface::class);
        $this->objectRegistryFactory = $this->objectManager->create(ObjectRegistryFactory::class);
    }

    /**
     * Verify correct generate of the relative "StoreId"
     *
     * @param string $expect
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @magentoDataFixture Magento/CatalogUrlRewrite/_files/product_with_stores.php
     * @magentoDbIsolation disabled
     * @dataProvider getConfigGenerate
     */
    public function testGenerate(string $expect): void
    {
        $product = $this->productRepository->get('simple');
        $categories = $product->getCategoryCollection();
        $productCategories = $this->objectRegistryFactory->create(['entities' => $categories]);

        /** @var AnchorUrlRewriteGenerator $generator */
        $generator = $this->objectManager->get(AnchorUrlRewriteGenerator::class);

        /** @var $store Store */
        $store = Bootstrap::getObjectManager()->get(Store::class);
        $store->load('fixture_second_store', 'code');

        $urls = $generator->generate($store->getId(), $product, $productCategories);

        $this->assertEquals($expect, $urls[0]->getRequestPath());
    }

    /**
     * Data provider for testGenerate
     *
     * @return array
     */
    public function getConfigGenerate(): array
    {
        return [
            [
                'expect' => 'category-1-custom/simple-product.html'
            ]
        ];
    }
}
