<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    <extension_attributes for="Magento\Quote\Api\Data\CartInterface">
        <attribute code="quoteTestAttribute" type="Magento\User\Api\Data\UserInterface">
            <join reference_table="admin_user"
                  join_on_field="store_id"
                  reference_field="user_id"
                    >
                <field>firstname</field>
                <field>lastname</field>
                <field>email</field>
            </join>
        </attribute>
    </extension_attributes>
    <extension_attributes for="Magento\Quote\Api\Data\CartItemInterface">
        <attribute code="quoteItemTestAttribute" type="Magento\User\Api\Data\UserInterface">
            <join reference_table="admin_user"
                  join_on_field="store_id"
                  reference_field="user_id"
                    >
                <field>firstname</field>
                <field>lastname</field>
                <field>email</field>
            </join>
        </attribute>
    </extension_attributes>
</config>
