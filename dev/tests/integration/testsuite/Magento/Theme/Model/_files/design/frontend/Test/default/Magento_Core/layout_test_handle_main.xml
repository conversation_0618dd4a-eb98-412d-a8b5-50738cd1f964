<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<layout xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_layout.xsd">
    <update handle="layout_test_handle_sample"/>
    <referenceBlock name="header" remove="true"/>
    <referenceBlock name="menu" remove="true"/>
    <referenceBlock name="some_element_1" remove="true"/>
    <referenceBlock name="some_element_2" remove="true"/>
    <referenceBlock name="root">
        <action method="setTemplate">
            <argument name="template" xsi:type="string">popup.phtml</argument>
        </action>
    </referenceBlock>
    <referenceBlock name="some_element_1"/>
    <block name="test.nonexisting.block" class="Magento\Framework\View\Element\Text\ListText"/>
    <referenceBlock name="test.nonexisting.block" remove="true"/>
    <referenceBlock name="test.nonexisting.block">
        <action method="getSomething"/>
    </referenceBlock>
</layout>
