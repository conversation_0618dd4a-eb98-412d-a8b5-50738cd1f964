<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\TestFramework\Helper\Bootstrap;
use Magento\TestFramework\Workaround\Override\Fixture\Resolver;

$objectManager = Bootstrap::getObjectManager();

/** @var \Magento\Framework\Registry $registry */
$registry = $objectManager->get(\Magento\Framework\Registry::class);

$registry->unregister('isSecureArea');
$registry->register('isSecureArea', true);

/** @var \Magento\Catalog\Api\ProductRepositoryInterface $productRepository */
$productRepository = $objectManager->get(\Magento\Catalog\Api\ProductRepositoryInterface::class);

foreach (['simple_10', 'simple_20', 'configurable'] as $sku) {
    try {
        $product = $productRepository->get($sku, false, null, true);
        $productRepository->delete($product);
    } catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
    }
}
Resolver::getInstance()->requireDataFixture('Magento/Elasticsearch/_files/configurable_attribute_rollback.php');
Resolver::getInstance()->requireDataFixture('Magento/Elasticsearch/_files/select_attribute_rollback.php');
Resolver::getInstance()->requireDataFixture('Magento/Elasticsearch/_files/multiselect_attribute_rollback.php');

$registry->unregister('isSecureArea');
$registry->register('isSecureArea', false);
