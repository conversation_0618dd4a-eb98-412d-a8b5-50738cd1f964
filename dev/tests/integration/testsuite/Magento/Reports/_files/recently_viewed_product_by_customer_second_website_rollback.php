<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

use Magento\TestFramework\Workaround\Override\Fixture\Resolver;

Resolver::getInstance()->requireDataFixture('Magento/Catalog/_files/product_two_websites_rollback.php');
Resolver::getInstance()->requireDataFixture('Magento/Customer/_files/customer_for_second_website_rollback.php');
