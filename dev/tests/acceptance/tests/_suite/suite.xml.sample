<?xml version="1.0" encoding="UTF-8"?>
<!--
 /**
  * Copyright © Magento, Inc. All rights reserved.
  * See COPYING.txt for license details.
  */
-->
<suites xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:mftf:Suite/etc/suiteSchema.xsd">
    <suite name="sample">
        <before>
            <createData entity="defaultCategory" stepKey="createCategory"/>
        </before>
        <after>
            <deleteData createDataKey="createCategory" stepKey="deleteCategory"/>
        </after>
        <include>
            <module name="Catalog" />
            <group name="login" />
        </include>
        <exclude>
            <test name="AddImageToWYSIWYGCatalogEditor"/>
            <module name="Catalog" file="AdminCreateCategoryTest.xml" />
        </exclude>
    </suite>
</suites>
