diff --git a/vendor/hyva-themes/magento2-hyva-checkout/src/Magewire/Checkout/Shipping/MethodList.php b/vendor/hyva-themes/magento2-hyva-checkout/src/Magewire/Checkout/Shipping/MethodList.php
index 2f3696e40..c766088cd 100755
--- a/vendor/hyva-themes/magento2-hyva-checkout/src/Magewire/Checkout/Shipping/MethodList.php
+++ b/vendor/hyva-themes/magento2-hyva-checkout/src/Magewire/Checkout/Shipping/MethodList.php
@@ -82,8 +82,12 @@ class MethodList extends Component implements EvaluationInterface
             }
         } catch (CheckoutException $exception) {
             $this->dispatchErrorMessage($exception->getMessage());
+            $this->logger->critical('CheckoutException **** '.$exception->getMessage(), ['exception' => $exception]);
         } catch (LocalizedException $exception) {
             $this->dispatchErrorMessage('Something went wrong while saving your shipping preferences.');
+            $this->logger->critical('LocalizedException **** '.$exception->getMessage(), ['exception' => $exception]);
+        } catch (\Exception $exception) {
+            $this->logger->critical('Exception E **** '.$exception->getMessage(), ['exception' => $exception]);
         }
 
         return $value;
